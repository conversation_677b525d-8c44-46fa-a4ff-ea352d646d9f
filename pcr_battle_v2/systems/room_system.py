"""
房间系统

负责：
- 游戏房间的管理
- 玩家的加入和离开
- 房间状态的维护
- 游戏开始和结束的控制
"""

from typing import Dict, List, Optional, Any
import logging
import time
from ..core.event_bus import EventBus, AutoEventListener, event_handler
from ..core.entity import EntityManager, Entity
from ..core.config import config
from ..core.result import Result, CommonResults
from ..components.game_components import *
from ..components.character_components import PlayerComponent
from ..events.game_events import *


class RoomSystem(AutoEventListener):
    """房间系统"""
    
    def __init__(self, event_bus: EventBus, entity_manager: EntityManager):
        self.event_bus = event_bus
        self.entity_manager = entity_manager
        self.logger = logging.getLogger(__name__)
        
        super().__init__(event_bus)
    
    def create_room(self, room_id: str, group_id: int, creator_id: int, 
                   max_players: int = None) -> Result:
        """创建游戏房间"""
        try:
            # 检查是否已存在房间
            existing_room = self.get_room_by_group(group_id)
            if existing_room:
                return Result.failure("该群已有正在进行的游戏")
            
            # 创建房间实体
            room_entity = self.entity_manager.create_entity()
            
            # 添加房间组件
            room_comp = RoomComponent(
                room_id=room_id,
                group_id=group_id,
                creator_id=creator_id,
                max_players=max_players or config.game.max_players,
                state="waiting",
                created_at=time.time()
            )
            room_entity.add_component(room_comp)
            
            # 添加回合组件
            turn_comp = TurnComponent(
                turn_timeout=config.game.turn_timeout
            )
            room_entity.add_component(turn_comp)
            
            # 添加跑道组件
            runway_comp = RunwayComponent(size=config.game.runway_size)
            runway_comp.initialize_runway()
            room_entity.add_component(runway_comp)
            
            # 添加统计组件
            stats_comp = GameStatsComponent()
            room_entity.add_component(stats_comp)
            
            # 添加标签
            room_entity.add_tag("room")
            room_entity.add_tag(f"group_{group_id}")
            room_entity.add_tag(f"room_{room_id}")
            
            self.logger.info(f"Room created: {room_id} in group {group_id}")
            
            return Result.success_with_data(room_entity)
            
        except Exception as e:
            self.logger.error(f"Error creating room: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def get_room_by_group(self, group_id: int) -> Optional[Entity]:
        """根据群号获取房间"""
        rooms = self.entity_manager.find_entities_with_tag(f"group_{group_id}")
        return rooms[0] if rooms else None
    
    def get_room_by_id(self, room_id: str) -> Optional[Entity]:
        """根据房间ID获取房间"""
        rooms = self.entity_manager.find_entities_with_tag(f"room_{room_id}")
        return rooms[0] if rooms else None
    
    def add_player_to_room(self, room: Entity, player: Entity) -> Result:
        """将玩家添加到房间"""
        try:
            room_comp = room.get_component(RoomComponent)
            if not room_comp:
                return Result.failure("房间组件缺失")
            
            # 检查房间状态
            if room_comp.state != "waiting":
                return Result.failure("游戏已开始，无法加入")
            
            # 检查房间是否已满
            current_players = self.get_room_players(room)
            if len(current_players) >= room_comp.max_players:
                return CommonResults.ROOM_FULL
            
            # 检查玩家是否已在房间中
            player_comp = player.get_component(PlayerComponent)
            if player_comp and player_comp.room_id == room_comp.room_id:
                return CommonResults.PLAYER_ALREADY_IN_ROOM
            
            # 更新玩家房间信息
            if player_comp:
                player_comp.room_id = room_comp.room_id
                player_comp.player_number = len(current_players) + 1
            
            # 添加玩家到房间标签
            player.add_tag(f"room_{room_comp.room_id}")
            
            self.logger.info(f"Player {player.entity_id} joined room {room_comp.room_id}")
            
            return Result.success_with_data({
                'room_id': room_comp.room_id,
                'player_count': len(current_players) + 1,
                'max_players': room_comp.max_players
            })
            
        except Exception as e:
            self.logger.error(f"Error adding player to room: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def remove_player_from_room(self, room: Entity, player: Entity, reason: str = "quit") -> Result:
        """将玩家从房间移除"""
        try:
            room_comp = room.get_component(RoomComponent)
            player_comp = player.get_component(PlayerComponent)
            
            if not room_comp or not player_comp:
                return Result.failure("组件缺失")
            
            # 移除房间标签
            player.remove_tag(f"room_{room_comp.room_id}")
            
            # 清空玩家房间信息
            player_comp.room_id = ""
            player_comp.player_number = 0
            
            # 发布玩家离开事件
            self.event_bus.publish_sync(PlayerLeftEvent(
                room_id=room_comp.room_id,
                group_id=room_comp.group_id,
                player_id=player_comp.user_id,
                player_name=player_comp.nickname,
                reason=reason
            ))
            
            self.logger.info(f"Player {player.entity_id} left room {room_comp.room_id} ({reason})")
            
            return Result.success_with_message("玩家已离开房间")
            
        except Exception as e:
            self.logger.error(f"Error removing player from room: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def get_room_players(self, room: Entity) -> List[Entity]:
        """获取房间中的所有玩家"""
        room_comp = room.get_component(RoomComponent)
        if not room_comp:
            return []
        
        return self.entity_manager.find_entities_with_tag(f"room_{room_comp.room_id}")
    
    def get_alive_players(self, room: Entity) -> List[Entity]:
        """获取房间中存活的玩家"""
        players = self.get_room_players(room)
        alive_players = []
        
        for player in players:
            if player.has_tag("character"):  # 只考虑角色实体
                state_comp = player.get_component(StateComponent)
                if state_comp and state_comp.is_alive:
                    alive_players.append(player)
        
        return alive_players
    
    def start_game(self, room: Entity) -> Result:
        """开始游戏"""
        try:
            room_comp = room.get_component(RoomComponent)
            if not room_comp:
                return Result.failure("房间组件缺失")
            
            # 检查房间状态
            if room_comp.state != "waiting":
                return Result.failure("游戏已开始")
            
            # 检查玩家数量
            players = self.get_room_players(room)
            if len(players) < 2:
                return Result.failure("至少需要2名玩家才能开始游戏")
            
            # 更新房间状态
            room_comp.set_state("character_selection")
            room_comp.started_at = time.time()
            
            # 发布游戏开始事件
            self.event_bus.publish_sync(GameStartedEvent(
                room_id=room_comp.room_id,
                group_id=room_comp.group_id,
                players=players,
                room_config=room_comp.settings
            ))
            
            self.logger.info(f"Game started in room {room_comp.room_id}")
            
            return Result.success_with_data({
                'room_id': room_comp.room_id,
                'state': room_comp.state,
                'players': len(players)
            })
            
        except Exception as e:
            self.logger.error(f"Error starting game: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def end_game(self, room: Entity, winner: Optional[Entity] = None) -> Result:
        """结束游戏"""
        try:
            room_comp = room.get_component(RoomComponent)
            if not room_comp:
                return Result.failure("房间组件缺失")
            
            # 更新房间状态
            room_comp.set_state("ended")
            room_comp.ended_at = time.time()
            
            # 计算游戏时长
            duration = room_comp.ended_at - (room_comp.started_at or room_comp.created_at)
            
            # 计算排名
            players = self.get_room_players(room)
            rankings = self._calculate_rankings(players, winner)
            
            # 发布游戏结束事件
            self.event_bus.publish_sync(GameEndedEvent(
                room_id=room_comp.room_id,
                group_id=room_comp.group_id,
                winner=winner,
                rankings=rankings,
                duration=duration
            ))
            
            self.logger.info(f"Game ended in room {room_comp.room_id}")
            
            return Result.success_with_data({
                'room_id': room_comp.room_id,
                'winner': winner,
                'rankings': rankings,
                'duration': duration
            })
            
        except Exception as e:
            self.logger.error(f"Error ending game: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def _calculate_rankings(self, players: List[Entity], winner: Optional[Entity]) -> Dict[int, Entity]:
        """计算玩家排名"""
        rankings = {}
        
        # 简单的排名逻辑：存活的玩家排在前面
        alive_players = []
        defeated_players = []
        
        for player in players:
            if player.has_tag("character"):
                state_comp = player.get_component(StateComponent)
                if state_comp and state_comp.is_alive:
                    alive_players.append(player)
                else:
                    defeated_players.append(player)
        
        # 分配排名
        rank = 1
        
        # 获胜者排第一
        if winner and winner in alive_players:
            rankings[rank] = winner
            rank += 1
            alive_players.remove(winner)
        
        # 其他存活玩家
        for player in alive_players:
            rankings[rank] = player
            rank += 1
        
        # 被击败的玩家
        for player in defeated_players:
            rankings[rank] = player
            rank += 1
        
        return rankings
    
    def get_room_status(self, room: Entity) -> Result:
        """获取房间状态"""
        try:
            room_comp = room.get_component(RoomComponent)
            turn_comp = room.get_component(TurnComponent)
            stats_comp = room.get_component(GameStatsComponent)
            
            if not room_comp:
                return Result.failure("房间组件缺失")
            
            players = self.get_room_players(room)
            alive_players = self.get_alive_players(room)
            
            status = {
                'room_id': room_comp.room_id,
                'group_id': room_comp.group_id,
                'state': room_comp.state,
                'max_players': room_comp.max_players,
                'player_count': len(players),
                'alive_count': len(alive_players),
                'created_at': room_comp.created_at,
                'started_at': room_comp.started_at,
                'ended_at': room_comp.ended_at
            }
            
            if turn_comp:
                status.update({
                    'current_turn': turn_comp.current_turn,
                    'current_player_index': turn_comp.current_player_index,
                    'phase': turn_comp.phase
                })
            
            if stats_comp:
                status.update({
                    'statistics': stats_comp.get_game_summary()
                })
            
            return Result.success_with_data(status)
            
        except Exception as e:
            self.logger.error(f"Error getting room status: {e}")
            return CommonResults.INTERNAL_ERROR
    
    @event_handler(PlayerDefeatedEvent)
    async def handle_player_defeated(self, event: PlayerDefeatedEvent):
        """处理玩家被击败事件"""
        if not event.defeated_player:
            return
        
        # 检查游戏是否结束
        player_comp = event.defeated_player.get_component(PlayerComponent)
        if not player_comp or not player_comp.room_id:
            return
        
        room = self.get_room_by_id(player_comp.room_id)
        if not room:
            return
        
        alive_players = self.get_alive_players(room)
        
        # 如果只剩一个玩家，游戏结束
        if len(alive_players) <= 1:
            winner = alive_players[0] if alive_players else None
            await self.end_game(room, winner)
    
    def cleanup_expired_rooms(self):
        """清理过期的房间"""
        try:
            current_time = time.time()
            all_rooms = self.entity_manager.find_entities_with_tag("room")
            expired_rooms = []
            
            for room in all_rooms:
                room_comp = room.get_component(RoomComponent)
                if not room_comp:
                    continue
                
                # 检查是否过期
                should_cleanup = False
                
                if room_comp.state == "waiting":
                    # 等待中的房间超过1小时
                    if current_time - room_comp.created_at > 3600:
                        should_cleanup = True
                elif room_comp.state == "ended":
                    # 已结束的房间超过10分钟
                    if room_comp.ended_at and current_time - room_comp.ended_at > 600:
                        should_cleanup = True
                
                if should_cleanup:
                    expired_rooms.append(room)
            
            # 清理过期房间
            for room in expired_rooms:
                self._cleanup_room(room)
            
            if expired_rooms:
                self.logger.info(f"Cleaned up {len(expired_rooms)} expired rooms")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up rooms: {e}")
    
    def _cleanup_room(self, room: Entity):
        """清理房间"""
        try:
            room_comp = room.get_component(RoomComponent)
            if room_comp:
                # 移除房间中的所有玩家
                players = self.get_room_players(room)
                for player in players:
                    self.remove_player_from_room(room, player, "room_cleanup")
                
                self.logger.info(f"Cleaned up room {room_comp.room_id}")
            
            # 移除房间实体
            self.entity_manager.remove_entity(room.entity_id)
            
        except Exception as e:
            self.logger.error(f"Error cleaning up room: {e}")
