"""
战斗系统

负责：
- 伤害计算和处理
- 防御力计算
- 暴击判定
- 生命值管理
- 击败判定
"""

from typing import Dict, List, Optional, Any
import logging
import random
import math
from ..core.event_bus import EventBus, AutoEventListener, event_handler
from ..core.entity import EntityManager, Entity
from ..core.config import config
from ..components.character_components import *
from ..events.combat_events import *


class BattleSystem(AutoEventListener):
    """战斗系统"""
    
    def __init__(self, event_bus: EventBus, entity_manager: EntityManager):
        self.event_bus = event_bus
        self.entity_manager = entity_manager
        self.logger = logging.getLogger(__name__)
        
        super().__init__(event_bus)
    
    @event_handler(DamageEvent)
    async def handle_damage(self, event: DamageEvent):
        """处理伤害事件"""
        if not event.target:
            return
        
        target_attr = event.target.get_component(AttributeComponent)
        if not target_attr:
            return
        
        # 计算最终伤害
        final_damage = event.damage
        damage_reduced = 0
        
        # 如果不是真实伤害，应用防御计算
        if event.damage_type != "true":
            final_damage = self.calculate_damage_with_defense(event.damage, target_attr.defense)
            damage_reduced = event.damage - final_damage
        
        # 检查暴击
        is_critical = event.is_critical
        if event.source and not is_critical:
            source_attr = event.source.get_component(AttributeComponent)
            if source_attr and self.check_critical_hit(source_attr.crit_rate):
                final_damage = int(final_damage * source_attr.crit_damage)
                is_critical = True
        
        # 应用伤害
        old_health = target_attr.health
        target_attr.health = max(0, target_attr.health - final_damage)
        
        # 发布伤害已造成事件
        await self.event_bus.publish(DamageDealtEvent(
            source=event.source,
            target=event.target,
            original_damage=event.damage,
            final_damage=final_damage,
            damage_reduced=damage_reduced,
            is_critical=is_critical
        ))
        
        # 检查是否被击败
        if target_attr.health <= 0:
            await self._handle_player_defeat(event.target, event.source)
    
    @event_handler(HealEvent)
    async def handle_heal(self, event: HealEvent):
        """处理治疗事件"""
        if not event.target:
            return
        
        target_attr = event.target.get_component(AttributeComponent)
        if not target_attr:
            return
        
        old_health = target_attr.health
        max_heal = target_attr.max_health - target_attr.health
        actual_heal = min(event.amount, max_heal)
        overheal = event.amount - actual_heal
        
        target_attr.health += actual_heal
        
        # 发布治疗已完成事件
        await self.event_bus.publish(HealingDoneEvent(
            source=event.source,
            target=event.target,
            original_amount=event.amount,
            final_amount=actual_heal,
            overheal=overheal
        ))
    
    async def _handle_player_defeat(self, defeated_player: Entity, killer: Optional[Entity]):
        """处理玩家击败"""
        state_comp = defeated_player.get_component(StateComponent)
        if state_comp:
            state_comp.is_alive = False
            state_comp.set_stage("out")
        
        # 检查是否有复活机制
        is_fake_death = False
        revival_turns = 0
        
        # 这里可以添加特殊角色的复活逻辑
        # 例如：某些角色有假死技能
        
        await self.event_bus.publish(PlayerDefeatedEvent(
            defeated_player=defeated_player,
            killer=killer,
            is_fake_death=is_fake_death,
            revival_turns=revival_turns
        ))
    
    def calculate_damage_with_defense(self, damage: int, defense: int) -> int:
        """根据防御力计算最终伤害"""
        # 防御力计算机制：
        # 100点防御力内，每1点防御力增加0.15%伤害减免
        # 到达100点防御力后，每一点防御力只可获得0.07%伤害减免
        # 500点防御力后，每1点防御力增加0.05%伤害减免
        # 最高有效防御力为1000（最高68%伤害减免）
        
        percent = 0.0
        effective_defense = min(defense, 1000)
        
        if effective_defense <= 100:
            percent = effective_defense * 0.0015
        else:
            percent = 100 * 0.0015  # 前100点的减免
            
            if effective_defense <= 500:
                percent += (effective_defense - 100) * 0.0007
            else:
                percent += 400 * 0.0007  # 100-500点的减免
                percent += (effective_defense - 500) * 0.0005
        
        # 应用伤害减免
        final_damage = damage * (1 - percent)
        return max(1, int(final_damage))  # 至少造成1点伤害
    
    def check_critical_hit(self, crit_rate: float) -> bool:
        """检查是否暴击"""
        return random.random() < crit_rate
    
    def calculate_distance(self, entity1: Entity, entity2: Entity) -> int:
        """计算两个实体之间的距离"""
        pos1 = entity1.get_component(PositionComponent)
        pos2 = entity2.get_component(PositionComponent)
        
        if not pos1 or not pos2:
            return 999  # 无法计算距离
        
        return pos1.distance_to(pos2.location)
    
    def can_attack(self, attacker: Entity, target: Entity) -> bool:
        """检查是否可以攻击目标"""
        # 检查距离
        distance = self.calculate_distance(attacker, target)
        attacker_attr = attacker.get_component(AttributeComponent)
        
        if not attacker_attr:
            return False
        
        if distance > attacker_attr.distance:
            return False
        
        # 检查目标是否存活
        target_state = target.get_component(StateComponent)
        if not target_state or not target_state.is_alive:
            return False
        
        return True
    
    def get_battle_statistics(self) -> Dict[str, Any]:
        """获取战斗统计信息"""
        all_characters = self.entity_manager.find_entities_with_tag("character")
        
        total_damage_dealt = 0
        total_healing_done = 0
        alive_count = 0
        
        for character in all_characters:
            state_comp = character.get_component(StateComponent)
            if state_comp and state_comp.is_alive:
                alive_count += 1
        
        return {
            "total_characters": len(all_characters),
            "alive_characters": alive_count,
            "defeated_characters": len(all_characters) - alive_count,
            "total_damage_dealt": total_damage_dealt,
            "total_healing_done": total_healing_done
        }
