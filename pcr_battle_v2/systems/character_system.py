"""
角色系统

负责：
- 角色的创建和管理
- 角色配置的加载
- 角色属性的计算
- 角色状态的维护
"""

from typing import Dict, List, Optional, Any
import logging
from ..core.event_bus import EventBus, AutoEventListener, event_handler
from ..core.entity import EntityManager, Entity
from ..core.config import config
from ..core.result import Result, CommonResults
from ..components.character_components import *
from ..events.game_events import CharacterSelectedEvent
from ..events.combat_events import AttributeChangedEvent


class CharacterSystem(AutoEventListener):
    """角色系统"""
    
    def __init__(self, event_bus: EventBus, entity_manager: EntityManager):
        self.event_bus = event_bus
        self.entity_manager = entity_manager
        self.logger = logging.getLogger(__name__)
        
        # 角色配置缓存
        self._character_configs: Dict[str, Dict[str, Any]] = {}
        self._loaded = False
        
        super().__init__(event_bus)
        
        # 加载角色配置
        self._load_character_configs()
    
    def _load_character_configs(self):
        """加载角色配置"""
        try:
            self._character_configs = config.get_all_character_configs()
            self._loaded = True
            self.logger.info(f"Loaded {len(self._character_configs)} character configurations")
        except Exception as e:
            self.logger.error(f"Error loading character configs: {e}")
            # 使用默认配置
            self._character_configs = self._get_default_characters()
            self._loaded = True
    
    def _get_default_characters(self) -> Dict[str, Dict[str, Any]]:
        """获取默认角色配置"""
        return {
            "diana": {
                "name": "嘉然",
                "description": "A-SOUL的虚拟偶像，拥有独特的嘉心糖机制",
                "position": "特殊型",
                "components": {
                    "attributes": {
                        "health": 1000,
                        "max_health": 1000,
                        "attack": 200,
                        "defense": 150,
                        "tp": 30,
                        "max_tp": 100,
                        "crit_rate": 0.1,
                        "crit_damage": 2.0,
                        "distance": 3
                    },
                    "skills": ["diana_attack", "diana_stream", "diana_poison"]
                }
            },
            "ava": {
                "name": "向晚",
                "description": "A-SOUL的虚拟偶像，擅长输出",
                "position": "输出型",
                "components": {
                    "attributes": {
                        "health": 900,
                        "max_health": 900,
                        "attack": 250,
                        "defense": 120,
                        "tp": 25,
                        "max_tp": 100,
                        "crit_rate": 0.15,
                        "crit_damage": 2.2,
                        "distance": 4
                    },
                    "skills": ["ava_attack", "ava_burst", "ava_dash"]
                }
            }
        }
    
    def get_character_list(self) -> Result:
        """获取角色列表"""
        try:
            if not self._loaded:
                return Result.failure("角色配置未加载")
            
            character_list = {}
            for char_id, config in self._character_configs.items():
                character_list[char_id] = {
                    'name': config.get('name', char_id),
                    'description': config.get('description', ''),
                    'position': config.get('position', '未知')
                }
            
            return Result.success_with_data(character_list)
            
        except Exception as e:
            self.logger.error(f"Error getting character list: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def get_character_info(self, character_id: str) -> Result:
        """获取角色详情"""
        try:
            if character_id not in self._character_configs:
                return CommonResults.CHARACTER_NOT_FOUND
            
            config = self._character_configs[character_id]
            
            # 构建角色信息
            char_info = {
                'id': character_id,
                'name': config.get('name', character_id),
                'description': config.get('description', ''),
                'position': config.get('position', '未知'),
                'attributes': config.get('components', {}).get('attributes', {}),
                'skills': []
            }
            
            # 获取技能信息（这里简化处理）
            skill_ids = config.get('components', {}).get('skills', [])
            for skill_id in skill_ids:
                # 这里应该从技能系统获取技能详情
                char_info['skills'].append({
                    'id': skill_id,
                    'name': skill_id.replace('_', ' ').title(),
                    'description': '技能描述',
                    'tp_cost': 10
                })
            
            return Result.success_with_data(char_info)
            
        except Exception as e:
            self.logger.error(f"Error getting character info: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def create_character(self, character_id: str, player_id: int) -> Result:
        """创建角色实体"""
        try:
            if character_id not in self._character_configs:
                return CommonResults.CHARACTER_NOT_FOUND
            
            config = self._character_configs[character_id]
            
            # 创建角色实体
            character = self.entity_manager.create_entity()
            
            # 添加元信息组件
            meta_comp = MetaComponent(
                name=config.get('name', character_id),
                description=config.get('description', ''),
                position=config.get('position', 'normal'),
                character_id=character_id
            )
            character.add_component(meta_comp)
            
            # 添加属性组件
            attr_config = config.get('components', {}).get('attributes', {})
            attr_comp = AttributeComponent(**attr_config)
            character.add_component(attr_comp)
            
            # 添加技能组件
            skill_ids = config.get('components', {}).get('skills', [])
            skill_comp = SkillComponent(skill_ids=skill_ids)
            character.add_component(skill_comp)
            
            # 添加位置组件
            pos_comp = PositionComponent()
            character.add_component(pos_comp)
            
            # 添加状态组件
            state_comp = StateComponent()
            character.add_component(state_comp)
            
            # 添加Buff组件
            buff_comp = BuffComponent()
            character.add_component(buff_comp)
            
            # 添加玩家组件
            player_comp = PlayerComponent(user_id=player_id)
            character.add_component(player_comp)
            
            # 添加角色标签
            character.add_tag("character")
            character.add_tag(f"character_{character_id}")
            character.add_tag(f"player_{player_id}")
            
            self.logger.info(f"Created character {character_id} for player {player_id}")
            
            return Result.success_with_data(character)
            
        except Exception as e:
            self.logger.error(f"Error creating character: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def modify_character_attribute(self, character: Entity, attribute: str, 
                                 value: int, is_permanent: bool = False) -> Result:
        """修改角色属性"""
        try:
            attr_comp = character.get_component(AttributeComponent)
            if not attr_comp:
                return Result.failure("角色缺少属性组件")
            
            old_value, new_value = attr_comp.modify_attribute(attribute, value, is_permanent)
            
            if old_value is None:
                return Result.failure(f"无效的属性名称: {attribute}")
            
            # 发布属性变化事件
            self.event_bus.publish_sync(AttributeChangedEvent(
                entity=character,
                attribute=attribute,
                old_value=old_value,
                new_value=new_value,
                change_amount=value
            ))
            
            return Result.success_with_data({
                'attribute': attribute,
                'old_value': old_value,
                'new_value': new_value,
                'change': value
            })
            
        except Exception as e:
            self.logger.error(f"Error modifying character attribute: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def get_character_status(self, character: Entity) -> Result:
        """获取角色状态"""
        try:
            meta_comp = character.get_component(MetaComponent)
            attr_comp = character.get_component(AttributeComponent)
            state_comp = character.get_component(StateComponent)
            pos_comp = character.get_component(PositionComponent)
            buff_comp = character.get_component(BuffComponent)
            
            if not all([meta_comp, attr_comp, state_comp, pos_comp]):
                return Result.failure("角色组件不完整")
            
            status = {
                'name': meta_comp.name,
                'character_id': meta_comp.character_id,
                'position_type': meta_comp.position,
                'health': attr_comp.health,
                'max_health': attr_comp.max_health,
                'tp': attr_comp.tp,
                'max_tp': attr_comp.max_tp,
                'attack': attr_comp.attack,
                'defense': attr_comp.defense,
                'crit_rate': attr_comp.crit_rate,
                'distance': attr_comp.distance,
                'location': pos_comp.location,
                'stage': state_comp.stage,
                'is_alive': state_comp.is_alive,
                'skip_turns': state_comp.skip_turns,
                'buffs': buff_comp.active_buffs.copy() if buff_comp else []
            }
            
            return Result.success_with_data(status)
            
        except Exception as e:
            self.logger.error(f"Error getting character status: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def find_characters_by_player(self, player_id: int) -> List[Entity]:
        """根据玩家ID查找角色"""
        return self.entity_manager.find_entities_with_tag(f"player_{player_id}")
    
    def find_character_by_id(self, character_id: str) -> Optional[Entity]:
        """根据角色ID查找角色"""
        characters = self.entity_manager.find_entities_with_tag(f"character_{character_id}")
        return characters[0] if characters else None
    
    def get_all_characters(self) -> List[Entity]:
        """获取所有角色"""
        return self.entity_manager.find_entities_with_tag("character")
    
    def get_alive_characters(self) -> List[Entity]:
        """获取存活的角色"""
        all_characters = self.get_all_characters()
        alive_characters = []
        
        for character in all_characters:
            state_comp = character.get_component(StateComponent)
            if state_comp and state_comp.is_alive:
                alive_characters.append(character)
        
        return alive_characters
    
    def validate_character(self, character: Entity) -> bool:
        """验证角色实体的完整性"""
        required_components = [
            MetaComponent,
            AttributeComponent,
            SkillComponent,
            PositionComponent,
            StateComponent,
            BuffComponent,
            PlayerComponent
        ]
        
        for comp_type in required_components:
            if not character.has_component(comp_type):
                return False
        
        # 验证组件数据
        for comp in character.get_all_components():
            if not comp.validate():
                return False
        
        return True
    
    @event_handler(CharacterSelectedEvent)
    async def handle_character_selected(self, event: CharacterSelectedEvent):
        """处理角色选择事件"""
        self.logger.info(f"Player {event.player_id} selected character {event.character_id}")
        
        # 这里可以添加角色选择后的处理逻辑
        # 例如：验证角色是否可用、更新房间状态等
    
    def reload_configs(self):
        """重新加载角色配置"""
        self.logger.info("Reloading character configurations...")
        self._load_character_configs()
