"""
技能系统

负责：
- 技能的定义和管理
- 技能效果的执行
- 技能目标的选择
- 技能冷却的管理
"""

from typing import Dict, List, Optional, Any, Tuple
import logging
import random
from ..core.event_bus import EventBus, AutoEventListener, event_handler
from ..core.entity import EntityManager, Entity
from ..core.config import config
from ..core.result import Result, CommonResults
from ..components.character_components import *
from ..events.player_events import SkillUsedEvent
from ..events.combat_events import *


class Skill:
    """技能类"""
    
    def __init__(self, skill_id: str, config: Dict[str, Any]):
        self.skill_id = skill_id
        self.name = config.get('name', skill_id)
        self.description = config.get('description', '')
        self.tp_cost = config.get('tp_cost', 0)
        self.target_type = config.get('target_type', 'single_enemy')
        self.cooldown = config.get('cooldown', 0)
        self.effects = config.get('effects', [])
        
    def can_use(self, caster: Entity) -> Tuple[bool, str]:
        """检查是否可以使用技能"""
        # 检查TP消耗
        attr_comp = caster.get_component(AttributeComponent)
        if not attr_comp:
            return False, "角色缺少属性组件"
            
        if attr_comp.tp < self.tp_cost:
            return False, "TP不足"
        
        # 检查冷却
        skill_comp = caster.get_component(SkillComponent)
        if skill_comp and not skill_comp.can_use_skill(self.skill_id):
            return False, "技能冷却中"
        
        return True, ""
    
    def get_info(self) -> Dict[str, Any]:
        """获取技能信息"""
        return {
            'id': self.skill_id,
            'name': self.name,
            'description': self.description,
            'tp_cost': self.tp_cost,
            'target_type': self.target_type,
            'cooldown': self.cooldown
        }


class SkillEffect:
    """技能效果基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.effect_type = config.get('type', 'unknown')
    
    def can_apply(self, context: 'SkillContext') -> bool:
        """检查是否可以应用此效果"""
        return True
    
    def apply(self, context: 'SkillContext') -> List[str]:
        """应用技能效果，返回消息列表"""
        return []


class DamageEffect(SkillEffect):
    """伤害效果"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.base_damage = config.get('base', 0)
        self.scaling = config.get('scaling', {})
        self.is_true_damage = config.get('true_damage', False)
    
    def apply(self, context: 'SkillContext') -> List[str]:
        messages = []
        
        for target in context.targets:
            damage = self._calculate_damage(context.caster, target)
            
            # 发布伤害事件
            context.event_bus.publish_sync(DamageEvent(
                source=context.caster,
                target=target,
                damage=damage,
                damage_type="true" if self.is_true_damage else "normal",
                skill_context=context
            ))
            
            caster_name = self._get_entity_name(context.caster)
            target_name = self._get_entity_name(target)
            messages.append(f"{target_name}受到了{damage}点伤害")
        
        return messages
    
    def _calculate_damage(self, caster: Entity, target: Entity) -> int:
        """计算伤害值"""
        caster_attr = caster.get_component(AttributeComponent)
        if not caster_attr:
            return 0
        
        damage = self.base_damage
        
        # 应用属性加成
        for attr_name, multiplier in self.scaling.items():
            if hasattr(caster_attr, attr_name):
                damage += getattr(caster_attr, attr_name) * multiplier
        
        return max(0, int(damage))
    
    def _get_entity_name(self, entity: Entity) -> str:
        """获取实体名称"""
        meta_comp = entity.get_component(MetaComponent)
        return meta_comp.name if meta_comp else "未知"


class HealEffect(SkillEffect):
    """治疗效果"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.base_heal = config.get('base', 0)
        self.scaling = config.get('scaling', {})
    
    def apply(self, context: 'SkillContext') -> List[str]:
        messages = []
        
        for target in context.targets:
            heal_amount = self._calculate_heal(context.caster, target)
            
            # 发布治疗事件
            context.event_bus.publish_sync(HealEvent(
                source=context.caster,
                target=target,
                amount=heal_amount,
                skill_context=context
            ))
            
            target_name = self._get_entity_name(target)
            messages.append(f"{target_name}回复了{heal_amount}点生命值")
        
        return messages
    
    def _calculate_heal(self, caster: Entity, target: Entity) -> int:
        """计算治疗量"""
        caster_attr = caster.get_component(AttributeComponent)
        if not caster_attr:
            return 0
        
        heal = self.base_heal
        
        for attr_name, multiplier in self.scaling.items():
            if hasattr(caster_attr, attr_name):
                heal += getattr(caster_attr, attr_name) * multiplier
        
        return max(0, int(heal))
    
    def _get_entity_name(self, entity: Entity) -> str:
        """获取实体名称"""
        meta_comp = entity.get_component(MetaComponent)
        return meta_comp.name if meta_comp else "未知"


class AttributeModifyEffect(SkillEffect):
    """属性修改效果"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.attribute = config.get('attribute', 'health')
        self.value = config.get('value', 0)
        self.scaling = config.get('scaling', {})
    
    def apply(self, context: 'SkillContext') -> List[str]:
        messages = []
        
        for target in context.targets:
            final_value = self._calculate_value(context.caster, target)
            
            attr_comp = target.get_component(AttributeComponent)
            if attr_comp:
                old_value, new_value = attr_comp.modify_attribute(self.attribute, final_value)
                
                # 发布属性变化事件
                context.event_bus.publish_sync(AttributeChangedEvent(
                    entity=target,
                    attribute=self.attribute,
                    old_value=old_value,
                    new_value=new_value,
                    change_amount=final_value,
                    skill_context=context
                ))
                
                target_name = self._get_entity_name(target)
                change_text = "增加" if final_value > 0 else "减少"
                attr_text = self._get_attribute_name(self.attribute)
                messages.append(f"{target_name}{change_text}了{abs(final_value)}点{attr_text}")
        
        return messages
    
    def _calculate_value(self, caster: Entity, target: Entity) -> int:
        """计算最终修改值"""
        value = self.value
        
        if self.scaling:
            source_attr = caster.get_component(AttributeComponent)
            if source_attr:
                for attr_name, multiplier in self.scaling.items():
                    if hasattr(source_attr, attr_name):
                        value += getattr(source_attr, attr_name) * multiplier
        
        return int(value)
    
    def _get_entity_name(self, entity: Entity) -> str:
        """获取实体名称"""
        meta_comp = entity.get_component(MetaComponent)
        return meta_comp.name if meta_comp else "未知"
    
    def _get_attribute_name(self, attribute: str) -> str:
        """获取属性中文名称"""
        attr_names = {
            'health': '生命值',
            'attack': '攻击力',
            'defense': '防御力',
            'tp': 'TP',
            'crit_rate': '暴击率',
            'distance': '攻击距离'
        }
        return attr_names.get(attribute, attribute)


class SkillContext:
    """技能上下文"""
    
    def __init__(self, caster: Entity, targets: List[Entity], skill: Skill, 
                 event_bus: EventBus, room_id: str = ""):
        self.caster = caster
        self.targets = targets
        self.skill = skill
        self.event_bus = event_bus
        self.room_id = room_id
        self.runtime_data = {}
    
    def get_runtime_data(self, key: str, default=None):
        """获取运行时数据"""
        return self.runtime_data.get(key, default)
    
    def set_runtime_data(self, key: str, value):
        """设置运行时数据"""
        self.runtime_data[key] = value


class SkillSystem(AutoEventListener):
    """技能系统"""
    
    def __init__(self, event_bus: EventBus, entity_manager: EntityManager):
        self.event_bus = event_bus
        self.entity_manager = entity_manager
        self.logger = logging.getLogger(__name__)
        
        # 技能配置缓存
        self._skill_configs: Dict[str, Dict[str, Any]] = {}
        self._skills: Dict[str, Skill] = {}
        self._effect_factories = {
            'damage': DamageEffect,
            'heal': HealEffect,
            'attribute_modify': AttributeModifyEffect
        }
        
        super().__init__(event_bus)
        
        # 加载技能配置
        self._load_skill_configs()
    
    def _load_skill_configs(self):
        """加载技能配置"""
        try:
            self._skill_configs = config.get_all_skill_configs()
            
            # 如果没有配置文件，使用默认配置
            if not self._skill_configs:
                self._skill_configs = self._get_default_skills()
            
            # 创建技能对象
            for skill_id, skill_config in self._skill_configs.items():
                self._skills[skill_id] = Skill(skill_id, skill_config)
            
            self.logger.info(f"Loaded {len(self._skills)} skills")
            
        except Exception as e:
            self.logger.error(f"Error loading skill configs: {e}")
            # 使用默认技能
            self._skill_configs = self._get_default_skills()
            for skill_id, skill_config in self._skill_configs.items():
                self._skills[skill_id] = Skill(skill_id, skill_config)
    
    def _get_default_skills(self) -> Dict[str, Dict[str, Any]]:
        """获取默认技能配置"""
        return {
            "diana_attack": {
                "name": "普通攻击",
                "description": "对目标造成伤害并回复生命值",
                "tp_cost": 10,
                "target_type": "single_enemy",
                "effects": [
                    {
                        "type": "damage",
                        "base": 0,
                        "scaling": {"attack": 1.0}
                    },
                    {
                        "type": "heal",
                        "base": 0,
                        "scaling": {"attack": 0.15}
                    }
                ]
            },
            "ava_attack": {
                "name": "普通攻击",
                "description": "对目标造成伤害",
                "tp_cost": 10,
                "target_type": "single_enemy",
                "effects": [
                    {
                        "type": "damage",
                        "base": 0,
                        "scaling": {"attack": 1.2}
                    }
                ]
            }
        }
    
    def get_skill(self, skill_id: str) -> Optional[Skill]:
        """获取技能对象"""
        return self._skills.get(skill_id)
    
    def get_skill_info(self, skill_id: str) -> Result:
        """获取技能信息"""
        skill = self.get_skill(skill_id)
        if not skill:
            return CommonResults.SKILL_NOT_FOUND
        
        return Result.success_with_data(skill.get_info())
    
    def get_character_skills(self, character: Entity) -> Result:
        """获取角色的技能列表"""
        try:
            skill_comp = character.get_component(SkillComponent)
            if not skill_comp:
                return Result.failure("角色缺少技能组件")
            
            skills_info = []
            for skill_id in skill_comp.skill_ids:
                skill = self.get_skill(skill_id)
                if skill:
                    info = skill.get_info()
                    info['can_use'] = skill_comp.can_use_skill(skill_id)
                    info['cooldown_remaining'] = skill_comp.cooldowns.get(skill_id, 0)
                    skills_info.append(info)
            
            return Result.success_with_data(skills_info)
            
        except Exception as e:
            self.logger.error(f"Error getting character skills: {e}")
            return CommonResults.INTERNAL_ERROR
    
    async def use_skill(self, caster: Entity, skill_id: str, targets: List[Entity], 
                       room_id: str = "") -> Result:
        """使用技能"""
        try:
            # 获取技能
            skill = self.get_skill(skill_id)
            if not skill:
                return CommonResults.SKILL_NOT_FOUND
            
            # 检查是否可以使用
            can_use, reason = skill.can_use(caster)
            if not can_use:
                return Result.failure(reason)
            
            # 消耗TP
            attr_comp = caster.get_component(AttributeComponent)
            if attr_comp:
                attr_comp.modify_attribute('tp', -skill.tp_cost)
            
            # 设置冷却
            skill_comp = caster.get_component(SkillComponent)
            if skill_comp and skill.cooldown > 0:
                skill_comp.set_cooldown(skill_id, skill.cooldown)
            
            # 创建技能上下文
            context = SkillContext(caster, targets, skill, self.event_bus, room_id)
            
            # 发布技能使用事件
            await self.event_bus.publish(SkillUsedEvent(
                player=caster,
                skill=skill,
                targets=targets,
                tp_cost=skill.tp_cost,
                room_id=room_id
            ))
            
            # 应用技能效果
            all_messages = []
            for effect_config in skill.effects:
                effect = self._create_effect(effect_config)
                if effect and effect.can_apply(context):
                    messages = effect.apply(context)
                    all_messages.extend(messages)
            
            return Result.success_with_data({
                'skill': skill.get_info(),
                'messages': all_messages
            })
            
        except Exception as e:
            self.logger.error(f"Error using skill: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def _create_effect(self, effect_config: Dict[str, Any]) -> Optional[SkillEffect]:
        """创建技能效果"""
        effect_type = effect_config.get('type', 'unknown')
        
        if effect_type in self._effect_factories:
            factory = self._effect_factories[effect_type]
            return factory(effect_config)
        
        self.logger.warning(f"Unknown effect type: {effect_type}")
        return None
    
    def select_targets(self, target_type: str, caster: Entity, specified_target: Optional[Entity],
                      all_characters: List[Entity]) -> List[Entity]:
        """选择技能目标"""
        if target_type == 'self':
            return [caster]
        
        elif target_type == 'single_enemy':
            if specified_target and specified_target != caster:
                return [specified_target]
            return []
        
        elif target_type == 'all_enemies':
            return [char for char in all_characters if char != caster]
        
        elif target_type == 'all':
            return all_characters
        
        elif target_type == 'random_enemy':
            enemies = [char for char in all_characters if char != caster]
            return [random.choice(enemies)] if enemies else []
        
        return []
    
    def register_effect_factory(self, effect_type: str, factory_class):
        """注册技能效果工厂"""
        self._effect_factories[effect_type] = factory_class
    
    def reload_configs(self):
        """重新加载技能配置"""
        self.logger.info("Reloading skill configurations...")
        self._load_skill_configs()
