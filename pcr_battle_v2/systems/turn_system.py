"""
回合系统

负责：
- 回合顺序的管理
- 回合阶段的切换
- 回合超时的处理
- 回合历史的记录
"""

from typing import Dict, List, Optional, Any
import logging
import time
import asyncio
from ..core.event_bus import EventBus, AutoEventListener, event_handler
from ..core.entity import EntityManager, Entity
from ..core.config import config
from ..core.result import Result, CommonResults
from ..components.game_components import *
from ..components.character_components import *
from ..events.game_events import *
from ..events.player_events import *


class TurnSystem(AutoEventListener):
    """回合系统"""
    
    def __init__(self, event_bus: EventBus, entity_manager: EntityManager):
        self.event_bus = event_bus
        self.entity_manager = entity_manager
        self.logger = logging.getLogger(__name__)
        
        # 回合超时任务
        self._timeout_tasks: Dict[str, asyncio.Task] = {}
        
        super().__init__(event_bus)
    
    def start_turn(self, room: <PERSON>ti<PERSON>, player: Entity) -> Result:
        """开始玩家回合"""
        try:
            room_comp = room.get_component(RoomComponent)
            turn_comp = room.get_component(TurnComponent)
            
            if not room_comp or not turn_comp:
                return Result.failure("房间或回合组件缺失")
            
            # 检查游戏状态
            if room_comp.state != "playing":
                return Result.failure("游戏未在进行中")
            
            # 更新回合信息
            turn_comp.current_turn += 1
            turn_comp.set_phase("dice")
            turn_comp.turn_start_time = time.time()
            
            # 更新玩家状态
            state_comp = player.get_component(StateComponent)
            if state_comp:
                state_comp.set_stage("dice")
            
            # 处理回合开始效果
            self._handle_turn_start_effects(player)
            
            # 发布回合开始事件
            self.event_bus.publish_sync(TurnStartedEvent(
                room_id=room_comp.room_id,
                group_id=room_comp.group_id,
                current_player=player,
                turn_number=turn_comp.current_turn,
                phase=turn_comp.phase
            ))
            
            # 设置回合超时
            self._set_turn_timeout(room, player)
            
            self.logger.info(f"Turn {turn_comp.current_turn} started for player {player.entity_id}")
            
            return Result.success_with_data({
                'turn_number': turn_comp.current_turn,
                'phase': turn_comp.phase,
                'player': player
            })
            
        except Exception as e:
            self.logger.error(f"Error starting turn: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def end_turn(self, room: Entity, player: Entity) -> Result:
        """结束玩家回合"""
        try:
            room_comp = room.get_component(RoomComponent)
            turn_comp = room.get_component(TurnComponent)
            
            if not room_comp or not turn_comp:
                return Result.failure("房间或回合组件缺失")
            
            # 取消超时任务
            self._cancel_turn_timeout(room)
            
            # 处理回合结束效果
            self._handle_turn_end_effects(player)
            
            # 更新玩家状态
            state_comp = player.get_component(StateComponent)
            if state_comp:
                state_comp.set_stage("waiting")
            
            # 记录回合历史
            turn_comp.add_turn_record(
                player_id=player.entity_id,
                action="turn_completed",
                details={"phase": turn_comp.phase}
            )
            
            # 发布回合结束事件
            self.event_bus.publish_sync(TurnEndedEvent(
                room_id=room_comp.room_id,
                group_id=room_comp.group_id,
                player=player,
                turn_number=turn_comp.current_turn
            ))
            
            self.logger.info(f"Turn {turn_comp.current_turn} ended for player {player.entity_id}")
            
            return Result.success_with_message("回合结束")
            
        except Exception as e:
            self.logger.error(f"Error ending turn: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def change_phase(self, room: Entity, new_phase: str) -> Result:
        """切换回合阶段"""
        try:
            room_comp = room.get_component(RoomComponent)
            turn_comp = room.get_component(TurnComponent)
            
            if not room_comp or not turn_comp:
                return Result.failure("房间或回合组件缺失")
            
            old_phase = turn_comp.phase
            turn_comp.set_phase(new_phase)
            
            # 重置超时
            current_player = self.get_current_player(room)
            if current_player:
                self._set_turn_timeout(room, current_player)
            
            # 发布阶段变化事件
            self.event_bus.publish_sync(PhaseChangedEvent(
                room_id=room_comp.room_id,
                group_id=room_comp.group_id,
                player=current_player,
                old_phase=old_phase,
                new_phase=new_phase
            ))
            
            self.logger.info(f"Phase changed from {old_phase} to {new_phase}")
            
            return Result.success_with_data({
                'old_phase': old_phase,
                'new_phase': new_phase
            })
            
        except Exception as e:
            self.logger.error(f"Error changing phase: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def get_current_player(self, room: Entity) -> Optional[Entity]:
        """获取当前回合的玩家"""
        try:
            turn_comp = room.get_component(TurnComponent)
            if not turn_comp:
                return None
            
            alive_players = self._get_alive_players_in_order(room)
            if not alive_players:
                return None
            
            # 根据当前玩家索引获取玩家
            if 0 <= turn_comp.current_player_index < len(alive_players):
                return alive_players[turn_comp.current_player_index]
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting current player: {e}")
            return None
    
    def get_next_player(self, room: Entity) -> Optional[Entity]:
        """获取下一个回合的玩家"""
        try:
            turn_comp = room.get_component(TurnComponent)
            if not turn_comp:
                return None
            
            alive_players = self._get_alive_players_in_order(room)
            if not alive_players:
                return None
            
            # 计算下一个玩家索引
            next_index = (turn_comp.current_player_index + 1) % len(alive_players)
            return alive_players[next_index]
            
        except Exception as e:
            self.logger.error(f"Error getting next player: {e}")
            return None
    
    def advance_to_next_player(self, room: Entity) -> Result:
        """推进到下一个玩家"""
        try:
            turn_comp = room.get_component(TurnComponent)
            if not turn_comp:
                return Result.failure("回合组件缺失")
            
            alive_players = self._get_alive_players_in_order(room)
            if not alive_players:
                return Result.failure("没有存活的玩家")
            
            # 更新玩家索引
            turn_comp.current_player_index = (turn_comp.current_player_index + 1) % len(alive_players)
            
            # 获取新的当前玩家
            next_player = alive_players[turn_comp.current_player_index]
            
            # 检查玩家是否需要跳过回合
            state_comp = next_player.get_component(StateComponent)
            if state_comp and state_comp.reduce_skip_turns():
                # 玩家需要跳过回合，继续到下一个玩家
                return self.advance_to_next_player(room)
            
            # 开始新玩家的回合
            return self.start_turn(room, next_player)
            
        except Exception as e:
            self.logger.error(f"Error advancing to next player: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def _get_alive_players_in_order(self, room: Entity) -> List[Entity]:
        """获取按顺序排列的存活玩家"""
        room_comp = room.get_component(RoomComponent)
        if not room_comp:
            return []
        
        # 获取房间中的所有角色
        all_players = self.entity_manager.find_entities_with_tag(f"room_{room_comp.room_id}")
        
        # 筛选存活的角色并按玩家编号排序
        alive_players = []
        for player in all_players:
            if player.has_tag("character"):
                state_comp = player.get_component(StateComponent)
                player_comp = player.get_component(PlayerComponent)
                
                if state_comp and state_comp.is_alive and player_comp:
                    alive_players.append((player_comp.player_number, player))
        
        # 按玩家编号排序
        alive_players.sort(key=lambda x: x[0])
        return [player for _, player in alive_players]
    
    def _handle_turn_start_effects(self, player: Entity):
        """处理回合开始效果"""
        try:
            # 增加TP
            attr_comp = player.get_component(AttributeComponent)
            if attr_comp:
                attr_comp.modify_attribute('tp', config.game.turn_tp_gain)
            
            # 减少技能冷却
            skill_comp = player.get_component(SkillComponent)
            if skill_comp:
                skill_comp.reduce_cooldowns()
            
            # 更新Buff
            buff_comp = player.get_component(BuffComponent)
            if buff_comp:
                expired_buffs = buff_comp.update_buffs()
                for buff in expired_buffs:
                    self.logger.info(f"Buff expired: {buff.get('type', 'unknown')}")
            
        except Exception as e:
            self.logger.error(f"Error handling turn start effects: {e}")
    
    def _handle_turn_end_effects(self, player: Entity):
        """处理回合结束效果"""
        try:
            # 这里可以添加回合结束时的特殊效果
            # 例如：毒伤、回复效果等
            pass
            
        except Exception as e:
            self.logger.error(f"Error handling turn end effects: {e}")
    
    def _set_turn_timeout(self, room: Entity, player: Entity):
        """设置回合超时"""
        try:
            room_comp = room.get_component(RoomComponent)
            turn_comp = room.get_component(TurnComponent)
            
            if not room_comp or not turn_comp:
                return
            
            # 取消之前的超时任务
            self._cancel_turn_timeout(room)
            
            # 创建新的超时任务
            timeout_task = asyncio.create_task(
                self._handle_turn_timeout(room, player, turn_comp.turn_timeout)
            )
            
            self._timeout_tasks[room_comp.room_id] = timeout_task
            
        except Exception as e:
            self.logger.error(f"Error setting turn timeout: {e}")
    
    def _cancel_turn_timeout(self, room: Entity):
        """取消回合超时"""
        try:
            room_comp = room.get_component(RoomComponent)
            if not room_comp:
                return
            
            if room_comp.room_id in self._timeout_tasks:
                task = self._timeout_tasks[room_comp.room_id]
                if not task.done():
                    task.cancel()
                del self._timeout_tasks[room_comp.room_id]
                
        except Exception as e:
            self.logger.error(f"Error canceling turn timeout: {e}")
    
    async def _handle_turn_timeout(self, room: Entity, player: Entity, timeout_seconds: int):
        """处理回合超时"""
        try:
            await asyncio.sleep(timeout_seconds)
            
            # 发布超时事件
            room_comp = room.get_component(RoomComponent)
            turn_comp = room.get_component(TurnComponent)
            
            if room_comp and turn_comp:
                await self.event_bus.publish(PlayerTimeoutEvent(
                    room_id=room_comp.room_id,
                    player=player,
                    timeout_type=turn_comp.phase,
                    timeout_duration=timeout_seconds
                ))
                
                # 自动结束回合
                self.end_turn(room, player)
                
                # 推进到下一个玩家
                self.advance_to_next_player(room)
            
        except asyncio.CancelledError:
            # 超时任务被取消，正常情况
            pass
        except Exception as e:
            self.logger.error(f"Error handling turn timeout: {e}")
    
    @event_handler(DiceRolledEvent)
    async def handle_dice_rolled(self, event: DiceRolledEvent):
        """处理骰子投掷事件"""
        if not event.player:
            return
        
        # 获取玩家所在房间
        player_comp = event.player.get_component(PlayerComponent)
        if not player_comp or not player_comp.room_id:
            return
        
        # 获取房间
        rooms = self.entity_manager.find_entities_with_tag(f"room_{player_comp.room_id}")
        if not rooms:
            return
        
        room = rooms[0]
        
        # 切换到技能阶段
        await self.change_phase(room, "skill")
    
    @event_handler(SkillUsedEvent)
    async def handle_skill_used(self, event: SkillUsedEvent):
        """处理技能使用事件"""
        if not event.player:
            return
        
        # 获取玩家所在房间
        player_comp = event.player.get_component(PlayerComponent)
        if not player_comp or not player_comp.room_id:
            return
        
        # 获取房间
        rooms = self.entity_manager.find_entities_with_tag(f"room_{player_comp.room_id}")
        if not rooms:
            return
        
        room = rooms[0]
        
        # 结束当前回合
        self.end_turn(room, event.player)
        
        # 推进到下一个玩家
        self.advance_to_next_player(room)
    
    def get_turn_statistics(self, room: Entity) -> Dict[str, Any]:
        """获取回合统计信息"""
        try:
            turn_comp = room.get_component(TurnComponent)
            if not turn_comp:
                return {}
            
            return {
                'current_turn': turn_comp.current_turn,
                'current_player_index': turn_comp.current_player_index,
                'phase': turn_comp.phase,
                'turn_history_count': len(turn_comp.turn_history),
                'turn_timeout': turn_comp.turn_timeout
            }
            
        except Exception as e:
            self.logger.error(f"Error getting turn statistics: {e}")
            return {}
