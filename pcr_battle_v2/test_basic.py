"""
基础功能测试

测试新架构的核心功能是否正常工作
"""

import asyncio
import logging
from core.game_manager import GameManager
from core.config import config
from systems.character_system import CharacterSystem
from systems.skill_system import SkillSystem
from systems.battle_system import BattleSystem

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_basic_functionality():
    """测试基础功能"""
    logger.info("开始测试PCR Battle V2基础功能...")
    
    try:
        # 1. 测试游戏管理器初始化
        logger.info("1. 测试游戏管理器初始化...")
        game_manager = GameManager()
        logger.info("✓ 游戏管理器初始化成功")
        
        # 2. 测试房间创建
        logger.info("2. 测试房间创建...")
        result = await game_manager.create_room(group_id=12345, creator_id=67890)
        if result.success:
            logger.info(f"✓ 房间创建成功: {result.data}")
        else:
            logger.error(f"✗ 房间创建失败: {result.message}")
            return
        
        # 3. 测试玩家加入
        logger.info("3. 测试玩家加入...")
        result = await game_manager.join_room(group_id=12345, player_id=11111)
        if result.success:
            logger.info(f"✓ 玩家加入成功: {result.data}")
        else:
            logger.error(f"✗ 玩家加入失败: {result.message}")
        
        # 4. 测试角色列表获取
        logger.info("4. 测试角色列表获取...")
        result = await game_manager.get_character_list()
        if result.success:
            logger.info(f"✓ 角色列表获取成功: {len(result.data)}个角色")
            for char_id, char_info in result.data.items():
                logger.info(f"  - {char_info['name']} ({char_info['position']})")
        else:
            logger.error(f"✗ 角色列表获取失败: {result.message}")
        
        # 5. 测试角色详情获取
        logger.info("5. 测试角色详情获取...")
        result = await game_manager.get_character_info("嘉然")
        if result.success:
            char_info = result.data
            logger.info(f"✓ 角色详情获取成功: {char_info['name']}")
            logger.info(f"  描述: {char_info['description']}")
            logger.info(f"  属性: HP={char_info['attributes']['health']}, ATK={char_info['attributes']['attack']}")
        else:
            logger.error(f"✗ 角色详情获取失败: {result.message}")
        
        # 6. 测试配置系统
        logger.info("6. 测试配置系统...")
        logger.info(f"✓ 最大玩家数: {config.game.max_players}")
        logger.info(f"✓ 跑道大小: {config.game.runway_size}")
        logger.info(f"✓ 回合超时: {config.game.turn_timeout}秒")
        
        # 7. 测试角色系统
        logger.info("7. 测试角色系统...")
        character_system = CharacterSystem(game_manager.event_bus, game_manager.entity_manager)
        
        # 创建角色
        result = character_system.create_character("diana", 67890)
        if result.success:
            character = result.data
            logger.info(f"✓ 角色创建成功: {character.entity_id}")
            
            # 获取角色状态
            status_result = character_system.get_character_status(character)
            if status_result.success:
                status = status_result.data
                logger.info(f"  角色状态: {status['name']} HP={status['health']}/{status['max_health']}")
            
        else:
            logger.error(f"✗ 角色创建失败: {result.message}")
        
        # 8. 测试技能系统
        logger.info("8. 测试技能系统...")
        skill_system = SkillSystem(game_manager.event_bus, game_manager.entity_manager)
        
        # 获取技能信息
        skill_result = skill_system.get_skill_info("diana_attack")
        if skill_result.success:
            skill_info = skill_result.data
            logger.info(f"✓ 技能信息获取成功: {skill_info['name']}")
            logger.info(f"  描述: {skill_info['description']}")
            logger.info(f"  TP消耗: {skill_info['tp_cost']}")
        else:
            logger.error(f"✗ 技能信息获取失败: {skill_result.message}")
        
        logger.info("🎉 所有基础功能测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


async def test_event_system():
    """测试事件系统"""
    logger.info("开始测试事件系统...")

    try:
        game_manager = GameManager()

        # 创建两个角色
        char1_result = game_manager.character_system.create_character("diana", 1001)
        char2_result = game_manager.character_system.create_character("ava", 1002)

        if char1_result.success and char2_result.success:
            char1 = char1_result.data
            char2 = char2_result.data

            logger.info("✓ 创建了两个测试角色")

            # 测试伤害事件
            from events.combat_events import DamageEvent
            damage_event = DamageEvent(
                source=char1,
                target=char2,
                damage=100,
                damage_type="normal"
            )

            # 获取目标初始生命值
            from components.character_components import AttributeComponent
            char2_attr = char2.get_component(AttributeComponent)
            initial_health = char2_attr.health

            # 发布伤害事件
            await game_manager.event_bus.publish(damage_event)

            # 检查结果
            final_health = char2_attr.health
            damage_taken = initial_health - final_health

            logger.info(f"✓ 伤害事件测试完成")
            logger.info(f"  初始生命值: {initial_health}")
            logger.info(f"  最终生命值: {final_health}")
            logger.info(f"  实际伤害: {damage_taken}")

        else:
            logger.error("✗ 角色创建失败，无法测试事件系统")

    except Exception as e:
        logger.error(f"❌ 事件系统测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_skill_system():
    """测试技能系统"""
    logger.info("开始测试技能系统...")

    try:
        game_manager = GameManager()

        # 创建角色
        char_result = game_manager.character_system.create_character("diana", 1001)
        target_result = game_manager.character_system.create_character("ava", 1002)

        if char_result.success and target_result.success:
            caster = char_result.data
            target = target_result.data

            logger.info("✓ 创建了测试角色")

            # 获取角色技能
            skills_result = game_manager.skill_system.get_character_skills(caster)
            if skills_result.success:
                skills = skills_result.data
                logger.info(f"✓ 角色拥有 {len(skills)} 个技能")

                for skill in skills:
                    logger.info(f"  - {skill['name']} (TP消耗: {skill['tp_cost']})")

            # 测试技能使用
            skill_id = "diana_attack"
            skill_result = await game_manager.skill_system.use_skill(
                caster, skill_id, [target], "test_room"
            )

            if skill_result.success:
                logger.info("✓ 技能使用成功")
                messages = skill_result.data.get('messages', [])
                for message in messages:
                    logger.info(f"  消息: {message}")
            else:
                logger.error(f"✗ 技能使用失败: {skill_result.message}")

    except Exception as e:
        logger.error(f"❌ 技能系统测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_room_system():
    """测试房间系统"""
    logger.info("开始测试房间系统...")

    try:
        game_manager = GameManager()

        # 创建房间
        room_result = game_manager.room_system.create_room("test_room", 12345, 67890)
        if room_result.success:
            room = room_result.data
            logger.info("✓ 房间创建成功")

            # 创建玩家并加入房间
            player1 = game_manager.entity_manager.create_entity()
            from components.character_components import PlayerComponent
            player1.add_component(PlayerComponent(user_id=67890, nickname="玩家1"))

            player2 = game_manager.entity_manager.create_entity()
            player2.add_component(PlayerComponent(user_id=11111, nickname="玩家2"))

            # 添加玩家到房间
            add_result = game_manager.room_system.add_player_to_room(room, player2)
            if add_result.success:
                logger.info("✓ 玩家加入房间成功")

            # 获取房间状态
            status_result = game_manager.room_system.get_room_status(room)
            if status_result.success:
                status = status_result.data
                logger.info(f"✓ 房间状态: {status['state']}")
                logger.info(f"  玩家数量: {status['player_count']}/{status['max_players']}")

    except Exception as e:
        logger.error(f"❌ 房间系统测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_complete_workflow():
    """测试完整的游戏流程"""
    logger.info("开始测试完整游戏流程...")

    try:
        game_manager = GameManager()

        # 1. 创建房间
        logger.info("1. 创建房间...")
        result = await game_manager.create_room(group_id=12345, creator_id=67890)
        if not result.success:
            logger.error(f"房间创建失败: {result.message}")
            return

        # 2. 添加玩家
        logger.info("2. 添加玩家...")
        result = await game_manager.join_room(group_id=12345, player_id=11111)
        if not result.success:
            logger.error(f"玩家加入失败: {result.message}")
            return

        # 3. 开始游戏
        logger.info("3. 开始游戏...")
        result = await game_manager.start_game(group_id=12345, starter_id=67890)
        if result.success:
            logger.info("✓ 完整游戏流程测试成功")
        else:
            logger.error(f"游戏开始失败: {result.message}")

    except Exception as e:
        logger.error(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主测试函数"""
    print("=" * 60)
    print("PCR Battle V2 - 全面功能测试")
    print("=" * 60)

    await test_basic_functionality()
    print()
    await test_event_system()
    print()
    await test_skill_system()
    print()
    await test_room_system()
    print()
    await test_complete_workflow()

    print("=" * 60)
    print("🎉 所有测试完成！")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
