"""
图片渲染工具

负责生成游戏相关的图片内容
"""

from typing import Dict, List, Any, Optional
import io
import base64
from PIL import Image, ImageDraw, ImageFont
import logging


class ImageRenderer:
    """图片渲染器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 默认配置
        self.board_size = (800, 600)
        self.background_color = (255, 255, 255)
        self.grid_color = (200, 200, 200)
        self.player_colors = [
            (255, 100, 100),  # 红色
            (100, 255, 100),  # 绿色
            (100, 100, 255),  # 蓝色
            (255, 255, 100),  # 黄色
        ]
        
        # 尝试加载字体
        self.font = self._load_font()
    
    def _load_font(self, size: int = 16):
        """加载字体"""
        try:
            # 尝试加载系统字体
            return ImageFont.truetype("arial.ttf", size)
        except:
            try:
                # 尝试加载中文字体
                return ImageFont.truetype("msyh.ttf", size)
            except:
                # 使用默认字体
                return ImageFont.load_default()
    
    @staticmethod
    async def render_game_board(game_state: Dict[str, Any]) -> Optional[bytes]:
        """渲染游戏棋盘"""
        try:
            renderer = ImageRenderer()
            return await renderer._render_board_internal(game_state)
        except Exception as e:
            logging.getLogger(__name__).error(f"Error rendering game board: {e}")
            return None
    
    async def _render_board_internal(self, game_state: Dict[str, Any]) -> bytes:
        """内部棋盘渲染方法"""
        # 创建画布
        image = Image.new('RGB', self.board_size, self.background_color)
        draw = ImageDraw.Draw(image)
        
        # 绘制跑道
        self._draw_runway(draw, game_state)
        
        # 绘制玩家
        self._draw_players(draw, game_state)
        
        # 绘制UI信息
        self._draw_ui_info(draw, game_state)
        
        # 转换为字节数据
        return self._image_to_bytes(image)
    
    def _draw_runway(self, draw: ImageDraw.Draw, game_state: Dict[str, Any]):
        """绘制跑道"""
        runway_size = game_state.get('runway_size', 36)
        center_x, center_y = self.board_size[0] // 2, self.board_size[1] // 2
        radius = min(center_x, center_y) - 100
        
        # 绘制跑道圆环
        bbox = [
            center_x - radius, center_y - radius,
            center_x + radius, center_y + radius
        ]
        draw.ellipse(bbox, outline=self.grid_color, width=3)
        
        # 绘制跑道格子
        import math
        for i in range(runway_size):
            angle = 2 * math.pi * i / runway_size
            x = center_x + radius * math.cos(angle)
            y = center_y + radius * math.sin(angle)
            
            # 绘制格子标记
            draw.ellipse([x-5, y-5, x+5, y+5], fill=self.grid_color)
            
            # 绘制格子编号
            text = str(i)
            text_bbox = draw.textbbox((0, 0), text, font=self.font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            draw.text(
                (x - text_width//2, y - text_height//2 - 15),
                text, fill=(0, 0, 0), font=self.font
            )
    
    def _draw_players(self, draw: ImageDraw.Draw, game_state: Dict[str, Any]):
        """绘制玩家"""
        players = game_state.get('players', [])
        runway_size = game_state.get('runway_size', 36)
        center_x, center_y = self.board_size[0] // 2, self.board_size[1] // 2
        radius = min(center_x, center_y) - 100
        
        import math
        for i, player in enumerate(players):
            location = player.get('location', 0)
            name = player.get('name', f'玩家{i+1}')
            health = player.get('health', 0)
            max_health = player.get('max_health', 1000)
            
            # 计算玩家位置
            angle = 2 * math.pi * location / runway_size
            x = center_x + radius * math.cos(angle)
            y = center_y + radius * math.sin(angle)
            
            # 选择玩家颜色
            color = self.player_colors[i % len(self.player_colors)]
            
            # 绘制玩家圆圈
            player_radius = 15
            draw.ellipse([
                x - player_radius, y - player_radius,
                x + player_radius, y + player_radius
            ], fill=color, outline=(0, 0, 0), width=2)
            
            # 绘制玩家名称
            text_bbox = draw.textbbox((0, 0), name, font=self.font)
            text_width = text_bbox[2] - text_bbox[0]
            draw.text(
                (x - text_width//2, y + player_radius + 5),
                name, fill=(0, 0, 0), font=self.font
            )
            
            # 绘制生命值条
            self._draw_health_bar(draw, x, y + player_radius + 25, 
                                health, max_health, 60, 8)
    
    def _draw_health_bar(self, draw: ImageDraw.Draw, x: int, y: int, 
                        current: int, maximum: int, width: int, height: int):
        """绘制生命值条"""
        if maximum <= 0:
            return
        
        # 背景
        draw.rectangle([x - width//2, y, x + width//2, y + height], 
                      fill=(100, 100, 100), outline=(0, 0, 0))
        
        # 生命值
        health_width = int(width * current / maximum)
        if health_width > 0:
            color = (255, 0, 0) if current / maximum < 0.3 else (0, 255, 0)
            draw.rectangle([x - width//2, y, x - width//2 + health_width, y + height], 
                          fill=color)
        
        # 生命值文字
        text = f"{current}/{maximum}"
        text_bbox = draw.textbbox((0, 0), text, font=self.font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        draw.text(
            (x - text_width//2, y + height + 2),
            text, fill=(0, 0, 0), font=self.font
        )
    
    def _draw_ui_info(self, draw: ImageDraw.Draw, game_state: Dict[str, Any]):
        """绘制UI信息"""
        # 绘制回合信息
        current_player = game_state.get('current_player')
        turn_number = game_state.get('turn_number', 0)
        phase = game_state.get('phase', 'unknown')
        
        if current_player:
            player_name = current_player.get('name', '未知玩家')
            phase_text = {
                'dice': '投掷骰子',
                'skill': '使用技能'
            }.get(phase, '未知阶段')
            
            turn_text = f"回合 {turn_number} - {player_name}的{phase_text}阶段"
            
            # 绘制回合信息背景
            text_bbox = draw.textbbox((0, 0), turn_text, font=self.font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            bg_x = 10
            bg_y = 10
            bg_width = text_width + 20
            bg_height = text_height + 10
            
            draw.rectangle([bg_x, bg_y, bg_x + bg_width, bg_y + bg_height], 
                          fill=(255, 255, 255, 200), outline=(0, 0, 0))
            
            draw.text((bg_x + 10, bg_y + 5), turn_text, fill=(0, 0, 0), font=self.font)
        
        # 绘制游戏状态
        state = game_state.get('state', 'unknown')
        state_text = {
            'waiting': '等待中',
            'character_selection': '角色选择中',
            'playing': '游戏进行中',
            'ended': '已结束'
        }.get(state, '未知状态')
        
        # 右上角显示状态
        text_bbox = draw.textbbox((0, 0), state_text, font=self.font)
        text_width = text_bbox[2] - text_bbox[0]
        draw.text(
            (self.board_size[0] - text_width - 10, 10),
            state_text, fill=(0, 0, 0), font=self.font
        )
    
    def _image_to_bytes(self, image: Image.Image) -> bytes:
        """将图片转换为字节数据"""
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        return buffer.getvalue()
    
    @staticmethod
    def image_to_base64(image_bytes: bytes) -> str:
        """将图片字节转换为base64字符串"""
        return base64.b64encode(image_bytes).decode('utf-8')
    
    @staticmethod
    async def render_character_card(character_info: Dict[str, Any]) -> Optional[bytes]:
        """渲染角色卡片"""
        try:
            renderer = ImageRenderer()
            return await renderer._render_character_card_internal(character_info)
        except Exception as e:
            logging.getLogger(__name__).error(f"Error rendering character card: {e}")
            return None
    
    async def _render_character_card_internal(self, character_info: Dict[str, Any]) -> bytes:
        """内部角色卡片渲染方法"""
        # 创建画布
        card_size = (400, 600)
        image = Image.new('RGB', card_size, (240, 240, 240))
        draw = ImageDraw.Draw(image)
        
        # 绘制角色信息
        name = character_info.get('name', '未知角色')
        position = character_info.get('position', '未知')
        description = character_info.get('description', '暂无描述')
        
        y_offset = 20
        
        # 角色名称
        title_font = self._load_font(24)
        draw.text((20, y_offset), name, fill=(0, 0, 0), font=title_font)
        y_offset += 40
        
        # 角色定位
        draw.text((20, y_offset), f"定位：{position}", fill=(100, 100, 100), font=self.font)
        y_offset += 30
        
        # 角色描述
        # 简单的文字换行处理
        words = description.split()
        lines = []
        current_line = ""
        for word in words:
            test_line = current_line + word + " "
            if len(test_line) > 30:  # 简单的长度限制
                if current_line:
                    lines.append(current_line.strip())
                current_line = word + " "
            else:
                current_line = test_line
        if current_line:
            lines.append(current_line.strip())
        
        for line in lines:
            draw.text((20, y_offset), line, fill=(0, 0, 0), font=self.font)
            y_offset += 20
        
        y_offset += 20
        
        # 属性信息
        if 'attributes' in character_info:
            attrs = character_info['attributes']
            draw.text((20, y_offset), "【基础属性】", fill=(0, 0, 0), font=self.font)
            y_offset += 25
            
            attr_list = [
                f"生命值：{attrs.get('health', 0)}",
                f"攻击力：{attrs.get('attack', 0)}",
                f"防御力：{attrs.get('defense', 0)}",
                f"TP：{attrs.get('tp', 0)}"
            ]
            
            for attr_text in attr_list:
                draw.text((40, y_offset), attr_text, fill=(0, 0, 0), font=self.font)
                y_offset += 20
        
        return self._image_to_bytes(image)
