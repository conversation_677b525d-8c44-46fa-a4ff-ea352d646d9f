"""
消息构建工具

负责构建各种游戏消息的文本内容
"""

from typing import Dict, List, Any, Optional
import time


class MessageBuilder:
    """消息构建器"""
    
    @staticmethod
    def build_character_list(characters: Dict[str, Dict[str, Any]]) -> str:
        """构建角色列表消息"""
        if not characters:
            return "暂无可用角色"
        
        lines = ["=== 可选角色列表 ==="]
        
        for char_id, char_info in characters.items():
            name = char_info.get('name', char_id)
            position = char_info.get('position', '未知')
            description = char_info.get('description', '暂无描述')
            
            lines.append(f"\n【{name}】({position})")
            lines.append(f"  {description}")
        
        lines.append("\n发送角色名称来选择角色，例如：嘉然")
        return "\n".join(lines)
    
    @staticmethod
    def build_character_info(char_info: Dict[str, Any]) -> str:
        """构建角色详情消息"""
        name = char_info.get('name', '未知角色')
        description = char_info.get('description', '暂无描述')
        position = char_info.get('position', '未知')
        
        lines = [f"=== {name} ==="]
        lines.append(f"定位：{position}")
        lines.append(f"描述：{description}")
        
        # 属性信息
        if 'attributes' in char_info:
            attrs = char_info['attributes']
            lines.append("\n【基础属性】")
            lines.append(f"生命值：{attrs.get('health', 0)}")
            lines.append(f"攻击力：{attrs.get('attack', 0)}")
            lines.append(f"防御力：{attrs.get('defense', 0)}")
            lines.append(f"TP：{attrs.get('tp', 0)}")
        
        # 技能信息
        if 'skills' in char_info:
            skills = char_info['skills']
            lines.append("\n【技能列表】")
            for i, skill in enumerate(skills, 1):
                skill_name = skill.get('name', f'技能{i}')
                skill_desc = skill.get('description', '暂无描述')
                tp_cost = skill.get('tp_cost', 0)
                lines.append(f"{i}. {skill_name}({tp_cost}TP)")
                lines.append(f"   {skill_desc}")
        
        return "\n".join(lines)
    
    @staticmethod
    def build_status_message(status_info: Dict[str, Any]) -> str:
        """构建游戏状态消息"""
        state = status_info.get('state', 'unknown')
        players = status_info.get('players', [])
        max_players = status_info.get('max_players', 4)
        
        lines = ["=== 大乱斗状态 ==="]
        
        # 游戏状态
        state_text = {
            'waiting': '等待中',
            'character_selection': '角色选择中',
            'playing': '游戏进行中',
            'ended': '已结束'
        }.get(state, '未知状态')
        
        lines.append(f"状态：{state_text}")
        lines.append(f"玩家：{len(players)}/{max_players}")
        
        # 玩家列表
        if players:
            lines.append("\n【玩家列表】")
            for i, player in enumerate(players, 1):
                name = player.get('name', f'玩家{player.get("user_id", i)}')
                char_name = player.get('character_name', '未选择')
                ready_status = "✓" if player.get('ready', False) else "✗"
                
                if state == 'character_selection':
                    lines.append(f"{i}. {name} - {char_name} {ready_status}")
                else:
                    lines.append(f"{i}. {name}")
        
        # 时间信息
        if 'created_at' in status_info:
            created_time = time.strftime(
                "%H:%M:%S", 
                time.localtime(status_info['created_at'])
            )
            lines.append(f"\n创建时间：{created_time}")
        
        if 'started_at' in status_info and status_info['started_at']:
            started_time = time.strftime(
                "%H:%M:%S", 
                time.localtime(status_info['started_at'])
            )
            lines.append(f"开始时间：{started_time}")
        
        return "\n".join(lines)
    
    @staticmethod
    def build_game_board_text(game_state: Dict[str, Any]) -> str:
        """构建游戏棋盘文本描述"""
        lines = ["=== 游戏棋盘 ==="]
        
        # 当前回合信息
        current_player = game_state.get('current_player')
        turn_number = game_state.get('turn_number', 0)
        phase = game_state.get('phase', 'unknown')
        
        if current_player:
            player_name = current_player.get('name', '未知玩家')
            phase_text = {
                'dice': '投掷骰子',
                'skill': '使用技能'
            }.get(phase, '未知阶段')
            
            lines.append(f"回合 {turn_number} - {player_name}的{phase_text}阶段")
        
        # 玩家状态
        players = game_state.get('players', [])
        if players:
            lines.append("\n【玩家状态】")
            for player in players:
                name = player.get('name', '未知')
                health = player.get('health', 0)
                max_health = player.get('max_health', 0)
                tp = player.get('tp', 0)
                location = player.get('location', 0)
                
                health_bar = MessageBuilder._build_health_bar(health, max_health)
                lines.append(f"{name} [{location}] {health_bar} TP:{tp}")
        
        return "\n".join(lines)
    
    @staticmethod
    def _build_health_bar(current: int, maximum: int, length: int = 10) -> str:
        """构建生命值条"""
        if maximum <= 0:
            return "[----------] 0/0"
        
        percentage = current / maximum
        filled = int(percentage * length)
        empty = length - filled
        
        bar = "█" * filled + "░" * empty
        return f"[{bar}] {current}/{maximum}"
    
    @staticmethod
    def build_skill_list(skills: List[Dict[str, Any]], player_tp: int = 0) -> str:
        """构建技能列表消息"""
        if not skills:
            return "该角色暂无技能"
        
        lines = ["=== 技能列表 ==="]
        
        for i, skill in enumerate(skills, 1):
            name = skill.get('name', f'技能{i}')
            description = skill.get('description', '暂无描述')
            tp_cost = skill.get('tp_cost', 0)
            
            # 检查是否有足够TP
            can_use = "✓" if player_tp >= tp_cost else "✗"
            
            lines.append(f"{i}. {name} ({tp_cost}TP) {can_use}")
            lines.append(f"   {description}")
        
        lines.append("\n发送技能编号来使用技能，例如：1")
        return "\n".join(lines)
    
    @staticmethod
    def build_damage_message(attacker: str, target: str, damage: int, 
                           is_critical: bool = False) -> str:
        """构建伤害消息"""
        crit_text = "暴击！" if is_critical else ""
        return f"{crit_text}{target}受到了{damage}点伤害"
    
    @staticmethod
    def build_heal_message(target: str, amount: int) -> str:
        """构建治疗消息"""
        return f"{target}回复了{amount}点生命值"
    
    @staticmethod
    def build_defeat_message(player: str, is_fake_death: bool = False, 
                           revival_turns: int = 0) -> str:
        """构建击败消息"""
        if is_fake_death:
            return f"{player}暂时出局，将在{revival_turns}回合后复活"
        else:
            return f"{player}出局"
    
    @staticmethod
    def build_game_result_message(rankings: Dict[int, str], 
                                duration: float) -> str:
        """构建游戏结果消息"""
        lines = ["=== 游戏结束 ==="]
        
        # 排名
        lines.append("\n【最终排名】")
        for rank, player_name in rankings.items():
            medal = {1: "🥇", 2: "🥈", 3: "🥉"}.get(rank, f"{rank}.")
            lines.append(f"{medal} {player_name}")
        
        # 游戏时长
        minutes = int(duration // 60)
        seconds = int(duration % 60)
        lines.append(f"\n游戏时长：{minutes}分{seconds}秒")
        
        return "\n".join(lines)
    
    @staticmethod
    def build_error_message(error: str) -> str:
        """构建错误消息"""
        return f"❌ {error}"
    
    @staticmethod
    def build_success_message(message: str) -> str:
        """构建成功消息"""
        return f"✅ {message}"
