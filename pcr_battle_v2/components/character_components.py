"""
角色相关组件定义
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from ..core.entity import Component


@dataclass
class MetaComponent(Component):
    """角色元信息组件"""
    name: str = ""
    description: str = ""
    position: str = "normal"  # 角色定位：输出型、防御型、爆发型、特殊型
    avatar_path: Optional[str] = None
    character_id: str = ""
    
    def validate(self) -> bool:
        return bool(self.name and self.character_id)


@dataclass
class AttributeComponent(Component):
    """属性组件 - 管理角色的数值属性"""
    # 基础属性
    health: int = 1000
    max_health: int = 1000
    attack: int = 200
    defense: int = 150
    tp: int = 30
    max_tp: int = 100
    
    # 战斗属性
    crit_rate: float = 0.1  # 暴击率 (0.0-1.0)
    crit_damage: float = 2.0  # 暴击伤害倍数
    distance: int = 3  # 攻击距离
    
    # 特殊属性
    attack_speed: int = 0  # 攻击速度（某些角色特有）
    cost_health: int = 0  # 已消耗生命值
    
    def modify_attribute(self, attr_name: str, value: int, is_permanent: bool = False) -> tuple[Any, Any]:
        """修改属性值"""
        if not hasattr(self, attr_name):
            return None, None
            
        old_value = getattr(self, attr_name)
        new_value = max(0, old_value + value)
        
        # 处理上限限制
        if attr_name == 'health':
            new_value = min(new_value, self.max_health)
        elif attr_name == 'tp':
            new_value = min(new_value, self.max_tp)
        elif attr_name == 'crit_rate':
            new_value = min(new_value, 1.0)
        elif attr_name == 'distance':
            new_value = min(new_value, 15)  # 最大攻击距离
            
        setattr(self, attr_name, new_value)
        
        # 更新已消耗生命值
        if attr_name in ('health', 'max_health'):
            self.cost_health = self.max_health - self.health
            
        return old_value, new_value
    
    def get_health_percentage(self) -> float:
        """获取生命值百分比"""
        if self.max_health <= 0:
            return 0.0
        return self.health / self.max_health
    
    def get_tp_percentage(self) -> float:
        """获取TP百分比"""
        if self.max_tp <= 0:
            return 0.0
        return self.tp / self.max_tp
    
    def is_alive(self) -> bool:
        """检查是否存活"""
        return self.health > 0
    
    def validate(self) -> bool:
        return (self.max_health > 0 and self.max_tp > 0 and 
                0 <= self.health <= self.max_health and
                0 <= self.tp <= self.max_tp and
                0 <= self.crit_rate <= 1.0)


@dataclass
class SkillComponent(Component):
    """技能组件 - 管理角色的技能"""
    skill_ids: List[str] = field(default_factory=list)  # 技能ID列表
    cooldowns: Dict[str, int] = field(default_factory=dict)  # 技能冷却
    
    def can_use_skill(self, skill_id: str) -> bool:
        """检查是否可以使用技能"""
        return self.cooldowns.get(skill_id, 0) <= 0
        
    def set_cooldown(self, skill_id: str, turns: int):
        """设置技能冷却"""
        if turns > 0:
            self.cooldowns[skill_id] = turns
        elif skill_id in self.cooldowns:
            del self.cooldowns[skill_id]
        
    def reduce_cooldowns(self):
        """减少所有技能冷却"""
        expired_skills = []
        for skill_id in self.cooldowns:
            self.cooldowns[skill_id] -= 1
            if self.cooldowns[skill_id] <= 0:
                expired_skills.append(skill_id)
                
        for skill_id in expired_skills:
            del self.cooldowns[skill_id]
    
    def get_available_skills(self) -> List[str]:
        """获取可用技能列表"""
        return [skill_id for skill_id in self.skill_ids 
                if self.can_use_skill(skill_id)]
    
    def validate(self) -> bool:
        return isinstance(self.skill_ids, list)


@dataclass
class PositionComponent(Component):
    """位置组件 - 管理角色在跑道上的位置"""
    location: int = 0
    runway_size: int = 36
    
    def move(self, steps: int):
        """移动指定步数"""
        self.location = (self.location + steps) % self.runway_size
        
    def move_to(self, location: int):
        """移动到指定位置"""
        self.location = location % self.runway_size
        
    def distance_to(self, other_location: int) -> int:
        """计算到另一个位置的距离"""
        direct_dist = abs(self.location - other_location)
        wrap_dist = self.runway_size - direct_dist
        return min(direct_dist, wrap_dist)
    
    def validate(self) -> bool:
        return (self.runway_size > 0 and 
                0 <= self.location < self.runway_size)


@dataclass
class StateComponent(Component):
    """状态组件 - 管理角色的游戏状态"""
    stage: str = "waiting"  # waiting, dice, skill, out, fake_out
    skip_turns: int = 0  # 跳过回合数
    is_alive: bool = True
    
    # 特殊状态标记
    special_flags: Dict[str, Any] = field(default_factory=dict)
    
    def set_stage(self, stage: str):
        """设置角色阶段"""
        valid_stages = ["waiting", "dice", "skill", "out", "fake_out"]
        if stage in valid_stages:
            self.stage = stage
        
    def add_skip_turns(self, turns: int):
        """增加跳过回合数"""
        self.skip_turns = max(0, self.skip_turns + turns)
        
    def reduce_skip_turns(self) -> bool:
        """减少跳过回合数，返回是否还需要跳过"""
        if self.skip_turns > 0:
            self.skip_turns -= 1
            return self.skip_turns > 0
        return False
    
    def set_flag(self, flag_name: str, value: Any):
        """设置特殊标记"""
        self.special_flags[flag_name] = value
        
    def get_flag(self, flag_name: str, default: Any = None) -> Any:
        """获取特殊标记"""
        return self.special_flags.get(flag_name, default)
    
    def remove_flag(self, flag_name: str):
        """移除特殊标记"""
        self.special_flags.pop(flag_name, None)
    
    def validate(self) -> bool:
        valid_stages = ["waiting", "dice", "skill", "out", "fake_out"]
        return (self.stage in valid_stages and 
                self.skip_turns >= 0)


@dataclass
class BuffComponent(Component):
    """Buff组件 - 管理角色身上的buff效果"""
    active_buffs: List[Dict[str, Any]] = field(default_factory=list)
    
    def add_buff(self, buff: Dict[str, Any]):
        """添加buff"""
        buff_type = buff.get('type', '')
        can_stack = buff.get('can_stack', False)
        
        # 检查是否已存在同类型buff
        existing = self.get_buff_by_type(buff_type)
        if existing:
            if can_stack:
                self.active_buffs.append(buff)
            else:
                # 替换现有buff
                self.remove_buff(existing)
                self.active_buffs.append(buff)
        else:
            self.active_buffs.append(buff)
            
    def remove_buff(self, buff: Dict[str, Any]):
        """移除buff"""
        if buff in self.active_buffs:
            self.active_buffs.remove(buff)
            
    def get_buff_by_type(self, buff_type: str) -> Optional[Dict[str, Any]]:
        """根据类型获取buff"""
        for buff in self.active_buffs:
            if buff.get('type') == buff_type:
                return buff
        return None
        
    def get_buffs_by_trigger(self, trigger_type: str) -> List[Dict[str, Any]]:
        """根据触发类型获取buff列表"""
        return [buff for buff in self.active_buffs 
                if buff.get('trigger_type') == trigger_type]
        
    def update_buffs(self) -> List[Dict[str, Any]]:
        """更新所有buff（减少持续时间，移除过期buff）"""
        expired_buffs = []
        
        for buff in self.active_buffs:
            if 'duration' in buff and buff['duration'] > 0:
                buff['duration'] -= 1
                if buff['duration'] <= 0:
                    expired_buffs.append(buff)
                    
        for buff in expired_buffs:
            self.remove_buff(buff)
            
        return expired_buffs
    
    def clear_buffs(self):
        """清除所有buff"""
        self.active_buffs.clear()
    
    def validate(self) -> bool:
        return isinstance(self.active_buffs, list)


@dataclass
class PlayerComponent(Component):
    """玩家信息组件"""
    user_id: int = 0
    nickname: str = ""
    room_id: str = ""
    player_number: int = 0  # 在房间中的编号
    
    # 游戏统计
    games_played: int = 0
    games_won: int = 0
    total_damage_dealt: int = 0
    total_damage_taken: int = 0
    
    def get_win_rate(self) -> float:
        """获取胜率"""
        if self.games_played <= 0:
            return 0.0
        return self.games_won / self.games_played
    
    def add_game_result(self, won: bool, damage_dealt: int = 0, damage_taken: int = 0):
        """添加游戏结果"""
        self.games_played += 1
        if won:
            self.games_won += 1
        self.total_damage_dealt += damage_dealt
        self.total_damage_taken += damage_taken
    
    def validate(self) -> bool:
        return (self.user_id > 0 and 
                self.games_played >= 0 and 
                self.games_won >= 0 and
                self.games_won <= self.games_played)
