"""
游戏相关组件定义
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from ..core.entity import Component


@dataclass
class RoomComponent(Component):
    """房间组件"""
    room_id: str = ""
    group_id: int = 0
    creator_id: int = 0
    max_players: int = 4
    state: str = "waiting"  # waiting, character_selection, playing, ended
    
    # 房间设置
    settings: Dict[str, Any] = field(default_factory=dict)
    
    # 时间信息
    created_at: float = 0.0
    started_at: Optional[float] = None
    ended_at: Optional[float] = None
    
    def is_waiting(self) -> bool:
        """是否在等待状态"""
        return self.state == "waiting"
    
    def is_playing(self) -> bool:
        """是否在游戏中"""
        return self.state == "playing"
    
    def is_ended(self) -> bool:
        """是否已结束"""
        return self.state == "ended"
    
    def set_state(self, new_state: str):
        """设置房间状态"""
        valid_states = ["waiting", "character_selection", "playing", "ended"]
        if new_state in valid_states:
            self.state = new_state
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """获取房间设置"""
        return self.settings.get(key, default)
    
    def set_setting(self, key: str, value: Any):
        """设置房间配置"""
        self.settings[key] = value
    
    def validate(self) -> bool:
        valid_states = ["waiting", "character_selection", "playing", "ended"]
        return (bool(self.room_id) and 
                self.group_id > 0 and 
                self.creator_id > 0 and
                self.max_players > 0 and
                self.state in valid_states)


@dataclass
class TurnComponent(Component):
    """回合组件"""
    current_turn: int = 0
    current_player_index: int = 0
    phase: str = "dice"  # dice, skill
    
    # 回合历史
    turn_history: List[Dict[str, Any]] = field(default_factory=list)
    
    # 回合设置
    turn_timeout: int = 60  # 秒
    turn_start_time: float = 0.0
    
    def next_turn(self):
        """进入下一回合"""
        self.current_turn += 1
        
    def set_phase(self, new_phase: str):
        """设置回合阶段"""
        valid_phases = ["dice", "skill"]
        if new_phase in valid_phases:
            self.phase = new_phase
    
    def is_dice_phase(self) -> bool:
        """是否是投掷骰子阶段"""
        return self.phase == "dice"
    
    def is_skill_phase(self) -> bool:
        """是否是使用技能阶段"""
        return self.phase == "skill"
    
    def add_turn_record(self, player_id: int, action: str, details: Dict[str, Any]):
        """添加回合记录"""
        record = {
            'turn': self.current_turn,
            'player_id': player_id,
            'action': action,
            'details': details,
            'timestamp': self.turn_start_time
        }
        self.turn_history.append(record)
    
    def get_turn_records(self, turn_number: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取回合记录"""
        if turn_number is None:
            return self.turn_history.copy()
        else:
            return [record for record in self.turn_history 
                   if record['turn'] == turn_number]
    
    def validate(self) -> bool:
        valid_phases = ["dice", "skill"]
        return (self.current_turn >= 0 and 
                self.current_player_index >= 0 and
                self.phase in valid_phases and
                self.turn_timeout > 0)


@dataclass
class RunwayComponent(Component):
    """跑道组件"""
    size: int = 36
    events: List[Dict[str, Any]] = field(default_factory=list)
    player_positions: Dict[int, int] = field(default_factory=dict)  # player_id -> location
    
    def initialize_runway(self):
        """初始化跑道事件"""
        import random
        
        # 跑道事件类型
        event_types = [
            {"type": "none", "weight": 40},
            {"type": "health", "weight": 20},
            {"type": "attack", "weight": 15},
            {"type": "defense", "weight": 15},
            {"type": "tp", "weight": 15},
            {"type": "move", "weight": 10}
        ]
        
        # 为每个位置随机分配事件
        self.events = []
        for i in range(self.size):
            # 根据权重随机选择事件类型
            total_weight = sum(event["weight"] for event in event_types)
            rand_num = random.randint(1, total_weight)
            
            current_weight = 0
            selected_event = "none"
            for event in event_types:
                current_weight += event["weight"]
                if rand_num <= current_weight:
                    selected_event = event["type"]
                    break
            
            self.events.append({
                "location": i,
                "type": selected_event,
                "triggered_count": 0
            })
    
    def get_event_at_location(self, location: int) -> Optional[Dict[str, Any]]:
        """获取指定位置的事件"""
        if 0 <= location < len(self.events):
            return self.events[location]
        return None
    
    def trigger_event_at_location(self, location: int) -> Optional[Dict[str, Any]]:
        """触发指定位置的事件"""
        event = self.get_event_at_location(location)
        if event:
            event["triggered_count"] += 1
            return event
        return None
    
    def set_player_position(self, player_id: int, location: int):
        """设置玩家位置"""
        self.player_positions[player_id] = location % self.size
    
    def get_player_position(self, player_id: int) -> int:
        """获取玩家位置"""
        return self.player_positions.get(player_id, 0)
    
    def move_player(self, player_id: int, steps: int) -> int:
        """移动玩家"""
        current_pos = self.get_player_position(player_id)
        new_pos = (current_pos + steps) % self.size
        self.set_player_position(player_id, new_pos)
        return new_pos
    
    def get_players_at_location(self, location: int) -> List[int]:
        """获取指定位置的所有玩家"""
        return [player_id for player_id, pos in self.player_positions.items() 
                if pos == location]
    
    def calculate_distance(self, pos1: int, pos2: int) -> int:
        """计算两个位置之间的距离"""
        direct_dist = abs(pos1 - pos2)
        wrap_dist = self.size - direct_dist
        return min(direct_dist, wrap_dist)
    
    def get_runway_state(self) -> Dict[str, Any]:
        """获取跑道状态"""
        return {
            "size": self.size,
            "events": self.events.copy(),
            "player_positions": self.player_positions.copy()
        }
    
    def validate(self) -> bool:
        return (self.size > 0 and 
                len(self.events) == self.size and
                all(0 <= pos < self.size for pos in self.player_positions.values()))


@dataclass
class GameStatsComponent(Component):
    """游戏统计组件"""
    total_turns: int = 0
    total_damage_dealt: int = 0
    total_healing_done: int = 0
    skills_used: int = 0
    events_triggered: int = 0
    
    # 玩家统计
    player_stats: Dict[int, Dict[str, Any]] = field(default_factory=dict)
    
    def add_player_stat(self, player_id: int, stat_name: str, value: Any):
        """添加玩家统计"""
        if player_id not in self.player_stats:
            self.player_stats[player_id] = {}
        
        if stat_name in self.player_stats[player_id]:
            if isinstance(value, (int, float)):
                self.player_stats[player_id][stat_name] += value
            else:
                self.player_stats[player_id][stat_name] = value
        else:
            self.player_stats[player_id][stat_name] = value
    
    def get_player_stat(self, player_id: int, stat_name: str, default: Any = 0) -> Any:
        """获取玩家统计"""
        return self.player_stats.get(player_id, {}).get(stat_name, default)
    
    def get_top_players(self, stat_name: str, limit: int = 3) -> List[tuple[int, Any]]:
        """获取指定统计的排行榜"""
        player_values = []
        for player_id, stats in self.player_stats.items():
            value = stats.get(stat_name, 0)
            player_values.append((player_id, value))
        
        # 按值降序排序
        player_values.sort(key=lambda x: x[1], reverse=True)
        return player_values[:limit]
    
    def get_game_summary(self) -> Dict[str, Any]:
        """获取游戏总结"""
        return {
            "total_turns": self.total_turns,
            "total_damage_dealt": self.total_damage_dealt,
            "total_healing_done": self.total_healing_done,
            "skills_used": self.skills_used,
            "events_triggered": self.events_triggered,
            "player_count": len(self.player_stats),
            "average_damage_per_player": (
                self.total_damage_dealt / len(self.player_stats) 
                if self.player_stats else 0
            )
        }
    
    def validate(self) -> bool:
        return (self.total_turns >= 0 and 
                self.total_damage_dealt >= 0 and
                self.total_healing_done >= 0 and
                self.skills_used >= 0 and
                self.events_triggered >= 0)
