# PCR Battle V2 迁移指南

## 🔄 从旧版本迁移到 V2

PCR Battle V2 是一个完全重新设计的版本，采用了全新的架构。本指南将帮助你了解新旧版本的差异，以及如何迁移到新版本。

## ⚠️ 重要提醒

**PCR Battle V2 与旧版本完全不兼容**。这是一个全新的实现，不支持直接迁移旧版本的数据或配置。

## 🆚 主要差异对比

### 架构差异

| 方面 | 旧版本 | V2 版本 |
|------|--------|---------|
| 架构模式 | 单体架构 | 组件化 + 事件驱动 |
| 代码组织 | 单一大文件 | 模块化分层 |
| 角色定义 | 硬编码 | 配置文件驱动 |
| 技能系统 | if-else 链 | 插件化效果 |
| 扩展性 | 困难 | 高度可扩展 |
| 测试性 | 难以测试 | 易于单元测试 |

### 功能对比

| 功能 | 旧版本 | V2 版本 | 状态 |
|------|--------|---------|------|
| 基础战斗 | ✅ | ✅ | 已实现 |
| 角色系统 | ✅ | ✅ | 已重新设计 |
| 技能系统 | ✅ | ✅ | 已重新设计 |
| Buff系统 | ✅ | ✅ | 已重新设计 |
| 跑道系统 | ✅ | ✅ | 已重新设计 |
| 图片生成 | ✅ | ✅ | 已重新设计 |
| 数据持久化 | ✅ | 🚧 | 计划中 |
| 排行榜 | ✅ | 🚧 | 计划中 |

## 🎯 迁移步骤

### 1. 备份旧版本

在开始迁移之前，请备份你的旧版本插件：

```bash
# 备份旧版本
cp -r pcr_scrimmage_debug pcr_scrimmage_backup
```

### 2. 安装新版本

```bash
# 下载新版本
git clone <repository_url> pcr_battle_v2

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置新版本

1. **复制配置文件**：
   ```bash
   cp pcr_battle_v2/config/config.json.example pcr_battle_v2/config/config.json
   ```

2. **调整配置**：
   根据你的需求修改 `config/config.json`

3. **配置 NoneBot**：
   在 `pyproject.toml` 中添加新插件：
   ```toml
   [tool.nonebot]
   plugins = ["pcr_battle_v2"]
   ```

### 4. 迁移角色数据

旧版本的角色数据无法直接迁移，但你可以参考以下步骤手动迁移：

#### 旧版本角色定义示例：
```python
"diana": {
    "name": "嘉然",
    "health": 1000,
    "attack": 200,
    "skills": [...]
}
```

#### 新版本角色配置：
```json
{
  "name": "嘉然",
  "description": "A-SOUL的虚拟偶像",
  "position": "特殊型",
  "components": {
    "attributes": {
      "health": 1000,
      "max_health": 1000,
      "attack": 200,
      "defense": 150,
      "tp": 30,
      "max_tp": 100
    },
    "skills": ["diana_attack", "diana_stream", "diana_poison"]
  }
}
```

### 5. 迁移技能数据

#### 旧版本技能定义：
```python
def skillEffect(self, skill_name, target):
    if skill_name == "diana_attack":
        damage = self.attack
        target.health -= damage
        self.health += damage * 0.15
```

#### 新版本技能配置：
```json
{
  "name": "普通攻击",
  "description": "对目标造成伤害并回复生命值",
  "tp_cost": 10,
  "target_type": "single_enemy",
  "effects": [
    {
      "type": "damage",
      "base": 0,
      "scaling": {"attack": 1.0}
    },
    {
      "type": "lifesteal",
      "ratio": 0.15
    }
  ]
}
```

## 🔧 自定义迁移工具

如果你有大量的自定义角色或技能，可以编写迁移脚本：

```python
import json

def migrate_character(old_char_data):
    """迁移角色数据"""
    new_char = {
        "name": old_char_data["name"],
        "description": old_char_data.get("description", ""),
        "position": old_char_data.get("position", "normal"),
        "components": {
            "attributes": {
                "health": old_char_data["health"],
                "max_health": old_char_data["health"],
                "attack": old_char_data["attack"],
                "defense": old_char_data.get("defense", 100),
                "tp": old_char_data.get("tp", 30),
                "max_tp": 100
            },
            "skills": old_char_data.get("skills", [])
        }
    }
    return new_char

# 使用示例
old_data = {...}  # 旧版本数据
new_data = migrate_character(old_data)

with open(f"config/characters/{char_id}.json", "w") as f:
    json.dump(new_data, f, indent=2, ensure_ascii=False)
```

## 🎮 命令对比

### 基础命令

| 功能 | 旧版本命令 | V2 版本命令 |
|------|------------|-------------|
| 创建房间 | `创建大乱斗` | `创建大乱斗` |
| 加入房间 | `加入大乱斗` | `加入大乱斗` |
| 开始游戏 | `开始大乱斗` | `开始大乱斗` |
| 查看状态 | `大乱斗状态` | `大乱斗状态` |
| 角色列表 | `大乱斗角色` | `大乱斗角色` |

### 游戏内命令

| 功能 | 旧版本命令 | V2 版本命令 |
|------|------------|-------------|
| 投掷骰子 | `丢` | `丢` |
| 使用技能 | 直接发送技能名 | `技能 [技能名]` |
| 认输 | `认输` | `认输` |

## 🐛 常见问题

### Q: 为什么不支持直接升级？
A: V2 版本采用了完全不同的架构设计，为了确保代码质量和未来的可维护性，我们选择了重新实现而不是修补旧版本。

### Q: 旧版本的游戏数据会丢失吗？
A: 是的，V2 版本无法读取旧版本的游戏数据。但你可以使用迁移工具来转换角色和技能配置。

### Q: V2 版本的性能如何？
A: V2 版本采用了更高效的事件驱动架构，理论上性能会比旧版本更好，特别是在处理复杂技能效果时。

### Q: 如何添加新角色？
A: 在 V2 版本中，添加新角色只需要创建配置文件，无需修改代码：
```bash
# 创建角色配置文件
touch config/characters/new_character.json
# 编辑配置文件
vim config/characters/new_character.json
```

### Q: 如何报告问题？
A: 请在项目的 GitHub 仓库中提交 Issue，并提供详细的错误信息和复现步骤。

## 📚 学习资源

- [V2 架构文档](README.md)
- [角色配置指南](docs/character_guide.md)
- [技能系统文档](docs/skill_system.md)
- [事件系统文档](docs/event_system.md)

## 🤝 获取帮助

如果在迁移过程中遇到问题，可以通过以下方式获取帮助：

1. 查看项目文档
2. 在 GitHub 上提交 Issue
3. 加入开发者交流群

---

**注意**: 迁移是一个复杂的过程，建议在测试环境中先进行完整的测试，确认一切正常后再在生产环境中部署。
