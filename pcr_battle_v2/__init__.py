"""
PCR Battle V2 - 全新的公主连结大乱斗插件

基于组件化和事件驱动的架构重新设计，具有以下特点：
- 高度模块化和可扩展
- 事件驱动的松耦合设计
- 数据驱动的角色和技能系统
- 支持插件化扩展

Author: Augment Agent
Version: 2.0.0
"""

from nonebot import on_command, on_fullmatch, on_message
from nonebot.adapters.onebot.v11 import GroupMessageEvent, Message, MessageSegment
from nonebot.params import CommandArg
from nonebot.permission import SUPERUSER
from nonebot.adapters.onebot.v11.permission import GROUP_ADMIN, GROUP_OWNER

from .core.game_manager import GameManager
from .core.config import Config
from .utils.message_utils import MessageBuilder
from .utils.image_utils import ImageRenderer

import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化游戏管理器
game_manager = GameManager()

# 注册命令处理器
create_room = on_fullmatch("创建大乱斗", priority=5, block=True)
join_room = on_fullmatch("加入大乱斗", priority=5, block=True)
start_game = on_fullmatch("开始大乱斗", priority=5, block=True)
end_game = on_fullmatch("结束大乱斗", priority=5, block=True)
game_status = on_fullmatch("大乱斗状态", priority=5, block=True)

# 角色相关命令
character_list = on_fullmatch("大乱斗角色", priority=5, block=True)
character_info = on_command("角色详情", priority=5, block=True)
select_character = on_message(priority=999, block=True)

# 游戏内命令
roll_dice = on_command("丢", aliases={"丢色子"}, priority=5, block=True)
use_skill = on_command("技能", priority=5, block=True)
surrender = on_fullmatch("认输", priority=5, block=True)
check_status = on_fullmatch("查看状态", priority=5, block=True)

# 管理命令
reload_config = on_command("重载配置", permission=SUPERUSER, priority=1, block=True)
debug_info = on_command("调试信息", permission=SUPERUSER, priority=1, block=True)


@create_room.handle()
async def handle_create_room(event: GroupMessageEvent):
    """创建游戏房间"""
    try:
        result = await game_manager.create_room(
            group_id=event.group_id,
            creator_id=event.user_id
        )
        
        if result.success:
            await create_room.send(f"大乱斗房间创建成功！房主：{result.data['creator_name']}")
        else:
            await create_room.send(f"创建失败：{result.message}")
            
    except Exception as e:
        logger.error(f"Error creating room: {e}")
        await create_room.send("创建房间时发生错误，请稍后重试")


@join_room.handle()
async def handle_join_room(event: GroupMessageEvent):
    """加入游戏房间"""
    try:
        result = await game_manager.join_room(
            group_id=event.group_id,
            player_id=event.user_id
        )
        
        if result.success:
            room_info = result.data
            player_count = len(room_info['players'])
            max_players = room_info['max_players']
            
            await join_room.send(
                f"成功加入大乱斗！当前玩家数：{player_count}/{max_players}"
            )
        else:
            await join_room.send(f"加入失败：{result.message}")
            
    except Exception as e:
        logger.error(f"Error joining room: {e}")
        await join_room.send("加入房间时发生错误，请稍后重试")


@start_game.handle()
async def handle_start_game(event: GroupMessageEvent):
    """开始游戏"""
    try:
        result = await game_manager.start_game(
            group_id=event.group_id,
            starter_id=event.user_id
        )
        
        if result.success:
            await start_game.send("大乱斗即将开始！请所有玩家选择角色")
            
            # 发送角色选择界面
            character_list_msg = await _build_character_list_message()
            await start_game.send(character_list_msg)
        else:
            await start_game.send(f"开始失败：{result.message}")
            
    except Exception as e:
        logger.error(f"Error starting game: {e}")
        await start_game.send("开始游戏时发生错误，请稍后重试")


@character_list.handle()
async def handle_character_list(event: GroupMessageEvent):
    """显示角色列表"""
    try:
        message = await _build_character_list_message()
        await character_list.send(message)
    except Exception as e:
        logger.error(f"Error showing character list: {e}")
        await character_list.send("获取角色列表时发生错误")


@character_info.handle()
async def handle_character_info(event: GroupMessageEvent, args: Message = CommandArg()):
    """显示角色详情"""
    try:
        character_name = args.extract_plain_text().strip()
        if not character_name:
            await character_info.send("请指定角色名称，例如：角色详情 嘉然")
            return
            
        result = await game_manager.get_character_info(character_name)
        
        if result.success:
            char_info = result.data
            message = MessageBuilder.build_character_info(char_info)
            await character_info.send(message)
        else:
            await character_info.send(f"获取角色信息失败：{result.message}")
            
    except Exception as e:
        logger.error(f"Error getting character info: {e}")
        await character_info.send("获取角色信息时发生错误")


@game_status.handle()
async def handle_game_status(event: GroupMessageEvent):
    """显示游戏状态"""
    try:
        result = await game_manager.get_room_status(event.group_id)
        
        if result.success:
            status_info = result.data
            message = MessageBuilder.build_status_message(status_info)
            
            # 如果游戏正在进行，生成游戏画面
            if status_info['state'] == 'playing':
                image_data = await ImageRenderer.render_game_board(status_info)
                if image_data:
                    message = Message([
                        MessageSegment.text(message),
                        MessageSegment.image(image_data)
                    ])
                    
            await game_status.send(message)
        else:
            await game_status.send(f"获取状态失败：{result.message}")
            
    except Exception as e:
        logger.error(f"Error getting game status: {e}")
        await game_status.send("获取游戏状态时发生错误")


@roll_dice.handle()
async def handle_roll_dice(event: GroupMessageEvent, args: Message = CommandArg()):
    """处理投掷骰子"""
    try:
        # 这里需要实现骰子投掷逻辑
        await roll_dice.send("骰子功能正在开发中...")
    except Exception as e:
        logger.error(f"Error rolling dice: {e}")
        await roll_dice.send("投掷骰子时发生错误")


@use_skill.handle()
async def handle_use_skill(event: GroupMessageEvent, args: Message = CommandArg()):
    """处理技能使用"""
    try:
        # 这里需要实现技能使用逻辑
        await use_skill.send("技能系统正在开发中...")
    except Exception as e:
        logger.error(f"Error using skill: {e}")
        await use_skill.send("使用技能时发生错误")


@surrender.handle()
async def handle_surrender(event: GroupMessageEvent):
    """处理认输"""
    try:
        # 这里需要实现认输逻辑
        await surrender.send("认输功能正在开发中...")
    except Exception as e:
        logger.error(f"Error surrendering: {e}")
        await surrender.send("认输时发生错误")


@check_status.handle()
async def handle_check_status(event: GroupMessageEvent):
    """查看游戏状态"""
    try:
        result = await game_manager.get_room_status(event.group_id)

        if result.success:
            status_info = result.data
            message = MessageBuilder.build_status_message(status_info)

            # 如果游戏正在进行，生成游戏画面
            if status_info.get('state') == 'playing':
                image_data = await ImageRenderer.render_game_board(status_info)
                if image_data:
                    message = Message([
                        MessageSegment.text(message),
                        MessageSegment.image(image_data)
                    ])

            await check_status.send(message)
        else:
            await check_status.send(f"获取状态失败：{result.message}")

    except Exception as e:
        logger.error(f"Error getting game status: {e}")
        await check_status.send("获取游戏状态时发生错误")


async def _build_character_list_message() -> str:
    """构建角色列表消息"""
    try:
        result = await game_manager.get_character_list()
        if result.success:
            return MessageBuilder.build_character_list(result.data)
        else:
            return "获取角色列表失败"
    except Exception as e:
        logger.error(f"Error building character list: {e}")
        return "构建角色列表时发生错误"


# 导出主要组件
__all__ = [
    'game_manager',
    'Config',
    'MessageBuilder',
    'ImageRenderer'
]
