# PCR Battle V2 - 公主连结大乱斗插件

## 🎮 项目简介

PCR Battle V2 是一个全新设计的公主连结大乱斗 NoneBot 插件，采用现代化的架构设计，具有高度的可扩展性和可维护性。

### ✨ 核心特性

- **🏗️ 组件化架构** - 基于 ECS (Entity Component System) 设计，模块化程度高
- **⚡ 事件驱动** - 使用事件总线实现松耦合的系统通信
- **🔧 插件化扩展** - 角色、技能、Buff 都可以通过配置文件动态加载
- **📊 数据驱动** - 游戏内容通过 JSON 配置文件定义，无需修改代码
- **🎯 高性能** - 异步事件处理，支持并发游戏
- **🧪 易测试** - 每个组件都可以独立进行单元测试

## 🏛️ 架构设计

### 系统层次结构

```
┌─────────────────────────────────────────────────────────────┐
│                    NoneBot Plugin Layer                     │
│                   (命令处理、消息发送)                        │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     Game Engine Core                        │
├─────────────────────────────────────────────────────────────┤
│  GameManager  │  EventBus  │  ComponentRegistry  │  Config  │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Game Systems Layer                       │
├─────────────────────────────────────────────────────────────┤
│ RoomSystem │ TurnSystem │ BattleSystem │ SkillSystem │ etc. │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Component Layer                           │
├─────────────────────────────────────────────────────────────┤
│  Character  │  Skill  │  Buff  │  Attribute  │  Position   │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     Data Layer                              │
├─────────────────────────────────────────────────────────────┤
│    JSON配置文件    │    动态加载器    │    数据验证器      │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 🎯 GameManager (游戏管理器)
- 游戏房间的创建和管理
- 玩家的加入和离开
- 与各个子系统的协调

#### 🚌 EventBus (事件总线)
- 事件发布/订阅机制
- 异步事件处理
- 事件优先级和过滤
- 事件历史记录

#### 🧩 Entity Component System
- **Entity**: 游戏中的实体对象
- **Component**: 纯数据容器
- **System**: 处理特定组件的逻辑

## 📁 项目结构

```
pcr_battle_v2/
├── __init__.py                 # NoneBot 插件入口
├── README.md                   # 项目文档
├── test_basic.py              # 基础功能测试
│
├── core/                      # 核心引擎
│   ├── __init__.py
│   ├── entity.py              # ECS 实体系统
│   ├── event_bus.py           # 事件总线
│   ├── game_manager.py        # 游戏管理器
│   ├── config.py              # 配置管理
│   └── result.py              # 统一结果类型
│
├── systems/                   # 游戏系统
│   ├── __init__.py
│   ├── character_system.py    # 角色系统
│   ├── skill_system.py        # 技能系统
│   ├── battle_system.py       # 战斗系统
│   ├── room_system.py         # 房间系统
│   └── turn_system.py         # 回合系统
│
├── components/                # 组件定义
│   ├── __init__.py
│   ├── character_components.py # 角色相关组件
│   └── game_components.py     # 游戏相关组件
│
├── events/                    # 事件定义
│   ├── __init__.py
│   ├── game_events.py         # 游戏流程事件
│   ├── combat_events.py       # 战斗相关事件
│   └── player_events.py       # 玩家行动事件
│
├── utils/                     # 工具模块
│   ├── __init__.py
│   ├── message_utils.py       # 消息构建工具
│   └── image_utils.py         # 图片渲染工具
│
└── config/                    # 配置文件
    ├── config.json            # 主配置文件
    ├── characters/            # 角色配置
    │   ├── diana.json
    │   └── ava.json
    ├── skills/                # 技能配置
    │   ├── diana_attack.json
    │   ├── diana_stream.json
    │   └── diana_poison.json
    └── buffs/                 # Buff配置
```

## 🚀 快速开始

### 安装依赖

```bash
pip install nonebot2
pip install nonebot-adapter-onebot
pip install pillow  # 用于图片生成
```

### 配置插件

1. 将 `pcr_battle_v2` 文件夹复制到你的 NoneBot 项目中
2. 在 `pyproject.toml` 中添加插件：

```toml
[tool.nonebot]
plugins = ["pcr_battle_v2"]
```

### 基本命令

- `创建大乱斗` - 创建游戏房间
- `加入大乱斗` - 加入游戏房间
- `开始大乱斗` - 开始游戏（房主专用）
- `大乱斗角色` - 查看可选角色
- `角色详情 [角色名]` - 查看角色详情
- `大乱斗状态` - 查看当前游戏状态

## 🎭 角色系统

### 角色组件

每个角色由多个组件组成：

- **MetaComponent** - 基本信息（名称、描述、定位）
- **AttributeComponent** - 数值属性（生命值、攻击力等）
- **SkillComponent** - 技能列表和冷却
- **PositionComponent** - 跑道位置
- **StateComponent** - 游戏状态
- **BuffComponent** - Buff效果

### 添加新角色

1. 在 `config/characters/` 目录下创建角色配置文件
2. 定义角色的属性、技能和特殊机制
3. 重启插件即可使用新角色

示例角色配置：

```json
{
  "name": "新角色",
  "description": "角色描述",
  "position": "输出型",
  "components": {
    "attributes": {
      "health": 1000,
      "attack": 250,
      "defense": 120,
      "tp": 25
    },
    "skills": ["skill1", "skill2", "skill3"]
  }
}
```

## ⚔️ 技能系统

### 技能效果类型

- **damage** - 伤害效果
- **heal** - 治疗效果
- **buff** - Buff效果
- **attribute_modify** - 属性修改
- **movement** - 移动效果

### 添加新技能

1. 在 `config/skills/` 目录下创建技能配置文件
2. 定义技能的效果和参数
3. 在角色配置中引用技能ID

示例技能配置：

```json
{
  "name": "新技能",
  "description": "技能描述",
  "tp_cost": 20,
  "target_type": "single_enemy",
  "effects": [
    {
      "type": "damage",
      "base": 100,
      "scaling": {"attack": 1.5}
    }
  ]
}
```

## 🧪 测试

运行基础功能测试：

```bash
cd pcr_battle_v2
python test_basic.py
```

## 🔧 配置选项

主要配置项在 `config/config.json` 中：

```json
{
  "game": {
    "max_players": 4,
    "runway_size": 36,
    "turn_timeout": 60
  },
  "system": {
    "debug_mode": false,
    "log_level": "INFO"
  },
  "bot": {
    "enable_image_rendering": true,
    "image_quality": 85
  }
}
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

---

**注意**: 这是一个全新的架构实现，与旧版本不兼容。如果你需要从旧版本迁移，请参考迁移指南。
