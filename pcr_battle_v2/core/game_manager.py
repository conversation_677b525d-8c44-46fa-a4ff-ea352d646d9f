"""
游戏管理器

负责：
- 游戏房间的创建和管理
- 玩家的加入和离开
- 游戏状态的维护
- 与各个子系统的协调
"""

from typing import Dict, List, Optional, Any
import asyncio
import logging
from dataclasses import dataclass, field
import time

from .result import Result, CommonResults
from .event_bus import EventBus
from .entity import EntityManager
from .config import config


@dataclass
class RoomInfo:
    """房间信息"""
    room_id: str
    group_id: int
    creator_id: int
    creator_name: str
    players: List[Dict[str, Any]] = field(default_factory=list)
    max_players: int = 4
    state: str = "waiting"  # waiting, character_selection, playing, ended
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    ended_at: Optional[float] = None


class GameManager:
    """游戏管理器 - 系统的总指挥"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 核心组件
        self.event_bus = EventBus(max_history=config.system.max_event_history)
        self.entity_manager = EntityManager()
        
        # 房间管理
        self._rooms: Dict[int, RoomInfo] = {}  # group_id -> RoomInfo
        self._player_rooms: Dict[int, int] = {}  # user_id -> group_id
        
        # 初始化游戏系统
        self._systems_initialized = False
        self._initialize_systems()

        self.logger.info("GameManager initialized")

    def _initialize_systems(self):
        """初始化各个子系统"""
        if self._systems_initialized:
            return

        try:
            from ..systems.room_system import RoomSystem
            from ..systems.character_system import CharacterSystem
            from ..systems.skill_system import SkillSystem
            from ..systems.battle_system import BattleSystem
            from ..systems.turn_system import TurnSystem

            # 初始化各个系统
            self.room_system = RoomSystem(self.event_bus, self.entity_manager)
            self.character_system = CharacterSystem(self.event_bus, self.entity_manager)
            self.skill_system = SkillSystem(self.event_bus, self.entity_manager)
            self.battle_system = BattleSystem(self.event_bus, self.entity_manager)
            self.turn_system = TurnSystem(self.event_bus, self.entity_manager)

            self._systems_initialized = True
            self.logger.info("Game systems initialized")
        except Exception as e:
            self.logger.error(f"Error initializing systems: {e}")
    
    async def create_room(self, group_id: int, creator_id: int) -> Result:
        """创建游戏房间"""
        try:
            # 检查是否已存在房间
            existing_room = self.room_system.get_room_by_group(group_id)
            if existing_room:
                return Result.failure("该群已有正在进行的游戏")

            # 检查创建者是否已在其他房间
            if creator_id in self._player_rooms:
                return Result.failure("您已在其他房间中，请先离开")

            # 获取创建者信息（这里简化处理）
            creator_name = f"玩家{creator_id}"
            room_id = f"room_{group_id}_{int(time.time())}"

            # 使用房间系统创建房间
            room_result = self.room_system.create_room(room_id, group_id, creator_id)
            if not room_result.success:
                return room_result

            room_entity = room_result.data

            # 创建创建者的玩家实体
            creator_entity = self.entity_manager.create_entity()
            from ..components.character_components import PlayerComponent
            player_comp = PlayerComponent(
                user_id=creator_id,
                nickname=creator_name,
                room_id=room_id,
                player_number=1
            )
            creator_entity.add_component(player_comp)
            creator_entity.add_tag("player")
            creator_entity.add_tag(f"room_{room_id}")

            # 保存映射关系
            self._player_rooms[creator_id] = group_id

            self.logger.info(f"Room created: {room_id} in group {group_id}")

            return Result.success_with_data({
                'room_id': room_id,
                'creator_name': creator_name,
                'max_players': config.game.max_players
            })

        except Exception as e:
            self.logger.error(f"Error creating room: {e}")
            return CommonResults.INTERNAL_ERROR
    
    async def join_room(self, group_id: int, player_id: int) -> Result:
        """加入游戏房间"""
        try:
            # 检查房间是否存在
            room_entity = self.room_system.get_room_by_group(group_id)
            if not room_entity:
                return CommonResults.ROOM_NOT_FOUND

            # 检查玩家是否在其他房间
            if player_id in self._player_rooms:
                return Result.failure("您已在其他房间中，请先离开")

            # 获取房间信息
            from ..components.game_components import RoomComponent
            room_comp = room_entity.get_component(RoomComponent)
            if not room_comp:
                return Result.failure("房间组件缺失")

            # 检查游戏状态
            if room_comp.state != "waiting":
                return Result.failure("游戏已开始，无法加入")

            # 检查房间是否已满
            current_players = self.room_system.get_room_players(room_entity)
            if len(current_players) >= room_comp.max_players:
                return CommonResults.ROOM_FULL

            # 获取玩家信息
            player_name = f"玩家{player_id}"

            # 创建玩家实体
            player_entity = self.entity_manager.create_entity()
            from ..components.character_components import PlayerComponent
            player_comp = PlayerComponent(
                user_id=player_id,
                nickname=player_name,
                room_id=room_comp.room_id,
                player_number=len(current_players) + 1
            )
            player_entity.add_component(player_comp)
            player_entity.add_tag("player")
            player_entity.add_tag(f"room_{room_comp.room_id}")

            # 保存映射关系
            self._player_rooms[player_id] = group_id

            self.logger.info(f"Player {player_id} joined room {room_comp.room_id}")

            return Result.success_with_data({
                'room_id': room_comp.room_id,
                'players': len(current_players) + 1,
                'max_players': room_comp.max_players
            })

        except Exception as e:
            self.logger.error(f"Error joining room: {e}")
            return CommonResults.INTERNAL_ERROR
    
    async def start_game(self, group_id: int, starter_id: int) -> Result:
        """开始游戏"""
        try:
            # 检查房间是否存在
            room_entity = self.room_system.get_room_by_group(group_id)
            if not room_entity:
                return CommonResults.ROOM_NOT_FOUND

            from ..components.game_components import RoomComponent
            room_comp = room_entity.get_component(RoomComponent)
            if not room_comp:
                return Result.failure("房间组件缺失")

            # 检查权限（只有房主可以开始游戏）
            if starter_id != room_comp.creator_id:
                return CommonResults.PERMISSION_DENIED

            # 使用房间系统开始游戏
            result = self.room_system.start_game(room_entity)
            if not result.success:
                return result

            self.logger.info(f"Game started in room {room_comp.room_id}")

            return result

        except Exception as e:
            self.logger.error(f"Error starting game: {e}")
            return CommonResults.INTERNAL_ERROR
    
    async def get_room_status(self, group_id: int) -> Result:
        """获取房间状态"""
        try:
            room_entity = self.room_system.get_room_by_group(group_id)
            if not room_entity:
                return CommonResults.ROOM_NOT_FOUND

            return self.room_system.get_room_status(room_entity)

        except Exception as e:
            self.logger.error(f"Error getting room status: {e}")
            return CommonResults.INTERNAL_ERROR
    
    async def get_character_list(self) -> Result:
        """获取角色列表"""
        try:
            return self.character_system.get_character_list()

        except Exception as e:
            self.logger.error(f"Error getting character list: {e}")
            return CommonResults.INTERNAL_ERROR
    
    async def get_character_info(self, character_name: str) -> Result:
        """获取角色详情"""
        try:
            # 根据角色名称查找角色ID
            char_list_result = self.character_system.get_character_list()
            if not char_list_result.success:
                return char_list_result

            character_id = None
            for char_id, char_info in char_list_result.data.items():
                if char_info['name'] == character_name:
                    character_id = char_id
                    break

            if not character_id:
                return CommonResults.CHARACTER_NOT_FOUND

            return self.character_system.get_character_info(character_id)

        except Exception as e:
            self.logger.error(f"Error getting character info: {e}")
            return CommonResults.INTERNAL_ERROR
    
    def get_room_by_group(self, group_id: int) -> Optional[RoomInfo]:
        """根据群号获取房间信息"""
        return self._rooms.get(group_id)
    
    def get_player_room(self, player_id: int) -> Optional[int]:
        """获取玩家所在的房间群号"""
        return self._player_rooms.get(player_id)
    
    def get_all_rooms(self) -> List[RoomInfo]:
        """获取所有房间"""
        return list(self._rooms.values())
    
    async def cleanup_expired_rooms(self):
        """清理过期的房间"""
        try:
            current_time = time.time()
            expired_rooms = []
            
            for group_id, room_info in self._rooms.items():
                # 如果房间创建超过1小时且未开始，或者结束超过10分钟
                if ((room_info.state == "waiting" and 
                     current_time - room_info.created_at > 3600) or
                    (room_info.state == "ended" and room_info.ended_at and
                     current_time - room_info.ended_at > 600)):
                    expired_rooms.append(group_id)
            
            for group_id in expired_rooms:
                await self._remove_room(group_id)
                
            if expired_rooms:
                self.logger.info(f"Cleaned up {len(expired_rooms)} expired rooms")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up rooms: {e}")
    
    async def _remove_room(self, group_id: int):
        """移除房间"""
        if group_id in self._rooms:
            room_info = self._rooms[group_id]
            
            # 移除玩家房间映射
            for player in room_info.players:
                self._player_rooms.pop(player['user_id'], None)
            
            # 移除房间
            del self._rooms[group_id]
            
            self.logger.info(f"Room removed: {room_info.room_id}")
