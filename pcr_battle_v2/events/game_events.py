"""
游戏流程相关事件
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from ..core.event_bus import Event
from ..core.entity import Entity


@dataclass
class GameEvent(Event):
    """游戏事件基类"""
    room_id: str = ""
    group_id: int = 0


@dataclass  
class GameStartedEvent(GameEvent):
    """游戏开始事件"""
    players: List[Entity] = field(default_factory=list)
    room_config: Dict = field(default_factory=dict)


@dataclass
class GameEndedEvent(GameEvent):
    """游戏结束事件"""
    winner: Optional[Entity] = None
    rankings: Dict[int, Entity] = field(default_factory=dict)
    duration: float = 0.0


@dataclass
class TurnStartedEvent(GameEvent):
    """回合开始事件"""
    current_player: Optional[Entity] = None
    turn_number: int = 0
    phase: str = "dice"  # dice, skill


@dataclass
class TurnEndedEvent(GameEvent):
    """回合结束事件"""
    player: Optional[Entity] = None
    turn_number: int = 0


@dataclass
class PhaseChangedEvent(GameEvent):
    """阶段变化事件"""
    player: Optional[Entity] = None
    old_phase: str = ""
    new_phase: str = ""


@dataclass
class RoomCreatedEvent(GameEvent):
    """房间创建事件"""
    creator_id: int = 0
    max_players: int = 4


@dataclass
class PlayerJoinedEvent(GameEvent):
    """玩家加入事件"""
    player_id: int = 0
    player_name: str = ""
    player_count: int = 0


@dataclass
class PlayerLeftEvent(GameEvent):
    """玩家离开事件"""
    player_id: int = 0
    player_name: str = ""
    reason: str = ""  # quit, timeout, kicked


@dataclass
class CharacterSelectedEvent(GameEvent):
    """角色选择事件"""
    player_id: int = 0
    character_id: str = ""
    character_name: str = ""


@dataclass
class AllPlayersReadyEvent(GameEvent):
    """所有玩家准备完毕事件"""
    players: List[Entity] = field(default_factory=list)


@dataclass
class RunwayEventTriggeredEvent(GameEvent):
    """跑道事件触发"""
    player: Optional[Entity] = None
    location: int = 0
    event_type: str = ""
    effect_value: int = 0
    effect_description: str = ""
