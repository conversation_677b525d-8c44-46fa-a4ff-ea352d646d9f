"""
游戏事件定义

包含所有游戏中使用的事件类型
"""

from .game_events import *
from .combat_events import *
from .player_events import *

__all__ = [
    # 游戏流程事件
    'GameStartedEvent',
    'GameEndedEvent', 
    'TurnStartedEvent',
    'TurnEndedEvent',
    'PhaseChangedEvent',
    'RoomCreatedEvent',
    'PlayerJoinedEvent',
    'PlayerLeftEvent',
    
    # 战斗事件
    'DamageEvent',
    'DamageDealtEvent',
    'HealEvent',
    'HealingDoneEvent',
    'PlayerDefeatedEvent',
    'PlayerRevivedEvent',
    
    # 玩家行动事件
    'PlayerActionEvent',
    'DiceRolledEvent',
    'SkillUsedEvent',
    'MovementEvent',
    
    # 属性和状态事件
    'AttributeChangedEvent',
    'BuffAppliedEvent',
    'BuffRemovedEvent',
    'StateChangedEvent'
]
