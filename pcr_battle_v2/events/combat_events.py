"""
战斗相关事件
"""

from dataclasses import dataclass, field
from typing import Optional, Any
from ..core.event_bus import Event
from ..core.entity import Entity


@dataclass
class CombatEvent(Event):
    """战斗事件基类"""
    source: Optional[Entity] = None
    target: Optional[Entity] = None


@dataclass
class DamageEvent(CombatEvent):
    """伤害事件"""
    damage: int = 0
    damage_type: str = "normal"  # normal, true, critical
    is_critical: bool = False
    skill_context: Optional[Any] = None
    
    
@dataclass
class DamageDealtEvent(CombatEvent):
    """伤害已造成事件（在防御计算后）"""
    original_damage: int = 0
    final_damage: int = 0
    damage_reduced: int = 0
    is_critical: bool = False


@dataclass
class HealEvent(CombatEvent):
    """治疗事件"""
    amount: int = 0
    heal_type: str = "normal"  # normal, lifesteal, regeneration
    skill_context: Optional[Any] = None


@dataclass
class HealingDoneEvent(CombatEvent):
    """治疗已完成事件"""
    original_amount: int = 0
    final_amount: int = 0
    overheal: int = 0


@dataclass
class PlayerDefeatedEvent(Event):
    """玩家被击败事件"""
    defeated_player: Optional[Entity] = None
    killer: Optional[Entity] = None
    is_fake_death: bool = False
    revival_turns: int = 0


@dataclass
class PlayerRevivedEvent(Event):
    """玩家复活事件"""
    revived_player: Optional[Entity] = None


@dataclass
class AttributeChangedEvent(Event):
    """属性变化事件"""
    entity: Optional[Entity] = None
    attribute: str = ""
    old_value: Any = None
    new_value: Any = None
    change_amount: Any = None
    skill_context: Optional[Any] = None


@dataclass
class BuffAppliedEvent(Event):
    """Buff应用事件"""
    target: Optional[Entity] = None
    buff: Optional[Any] = None
    source: Optional[Entity] = None
    skill_context: Optional[Any] = None


@dataclass
class BuffRemovedEvent(Event):
    """Buff移除事件"""
    target: Optional[Entity] = None
    buff: Optional[Any] = None
    reason: str = "expired"  # expired, dispelled, replaced


@dataclass
class BuffTriggeredEvent(Event):
    """Buff触发事件"""
    target: Optional[Entity] = None
    buff: Optional[Any] = None
    trigger_event: Optional[Event] = None


@dataclass
class StateChangedEvent(Event):
    """状态变化事件"""
    entity: Optional[Entity] = None
    old_state: str = ""
    new_state: str = ""
