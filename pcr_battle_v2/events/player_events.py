"""
玩家行动相关事件
"""

from dataclasses import dataclass, field
from typing import List, Optional, Any
from ..core.event_bus import Event
from ..core.entity import Entity


@dataclass
class PlayerActionEvent(Event):
    """玩家行动事件基类"""
    player: Optional[Entity] = None
    room_id: str = ""


@dataclass
class DiceRolledEvent(PlayerActionEvent):
    """骰子投掷事件"""
    dice_value: int = 0
    steps_moved: int = 0
    from_location: int = 0
    to_location: int = 0


@dataclass
class SkillUsedEvent(PlayerActionEvent):
    """技能使用事件"""
    skill: Optional[Any] = None
    targets: List[Entity] = field(default_factory=list)
    tp_cost: int = 0


@dataclass
class MovementEvent(PlayerActionEvent):
    """移动事件"""
    from_location: int = 0
    to_location: int = 0
    steps: int = 0
    trigger_runway_event: bool = True


@dataclass
class SkillTargetSelectedEvent(PlayerActionEvent):
    """技能目标选择事件"""
    skill: Optional[Any] = None
    target: Optional[Entity] = None


@dataclass
class PlayerTimeoutEvent(PlayerActionEvent):
    """玩家超时事件"""
    timeout_type: str = ""  # dice, skill, character_selection
    timeout_duration: int = 0


@dataclass
class PlayerSurrenderEvent(PlayerActionEvent):
    """玩家认输事件"""
    pass
